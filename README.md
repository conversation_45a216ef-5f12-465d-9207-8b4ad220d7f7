Clinic Management System (Java, console-based)

Overview
This project is a console-based clinic management system built in Java. It demonstrates custom set and queue ADTs, basic CRUD flows, and simple reporting across consultations, treatments, prescriptions, pharmacy inventory, and patients/doctors.

Key modules
- ADT: SetAndQueueInterface, SetQueueArray
- Entities: Patient, Doctor, Consultation, Treatment, Prescription, PrescribedMedicine, Medicine, PharmacyTransaction
- DAO: DataInitializer hardcodes all sample data (220 treatments, 220 prescriptions, full medicine list, transactions)
- Control: PatientManagement, DoctorManagement, ConsultationManagement, TreatmentManagement, PharmacyManagement
- Utility: Input validation and string helpers

How to run
1. Use JDK 17 or compatible.
2. Compile the project (e.g., with javac or your IDE). NetBeans build.xml is included.
3. Run the main entry at boundary.ClinicSystem.

Notes
- All sample data is hardcoded without loops as requested, including non-uniform diagnosis distribution in treatments.
- Prescriptions align with treatments and consultations; transactions mirror the first prescribed medicine per prescription.
- ADT methods used in controllers are annotated with inline comment: //adt method.

Folder structure
src contains source code organized by package (adt, boundary, control, dao, entity, utility). build contains compiled classes.