package dao;

import entity.Medicine;
import entity.Patient;
import entity.Doctor;
import entity.Consultation;
import entity.Treatment;
import entity.PharmacyTransaction;
import entity.Prescription;
import entity.PrescribedMedicine;
import adt.SetQueueArray;
import adt.SetAndQueueInterface;

public class DataInitializer {
    public static SetAndQueueInterface<Medicine> initializeSampleMedicines() {
        SetAndQueueInterface<Medicine> medicines = new SetQueueArray<>();

        medicines.add(new Medicine("MED001", "Paracetamol", "Panadol", 50, "2025-12-31", 8.50, "Pain Relief", "Paracetamol", "Analgesic"));
        medicines.add(new Medicine("MED002", "Amoxicillin", "Amoxil", 30, "2025-09-30", 15.80, "Antibiotic", "Amoxicillin", "Antibiotic"));
        medicines.add(new Medicine("MED003", "Ibuprofen", "Advil", 25, "2025-10-15", 12.90, "Pain Relief", "Ibuprofen", "NSAID"));
        medicines.add(new Medicine("MED004", "Omeprazole", "Losec", 15, "2025-11-20", 25.60, "Acid Reflux", "Omeprazole", "Proton Pump Inhibitor"));
        medicines.add(new Medicine("MED005", "Cetirizine", "Zyrtec", 5, "2025-12-10", 18.20, "Allergy Relief", "Cetirizine", "Antihistamine"));
        medicines.add(new Medicine("MED006", "Metformin", "Glucophage", 40, "2026-01-28", 22.40, "Diabetes Management", "Metformin", "Biguanide"));
        medicines.add(new Medicine("MED007", "Amlodipine", "Norvasc", 35, "2026-02-15", 32.80, "Hypertension", "Amlodipine", "Calcium Channel Blocker"));
        medicines.add(new Medicine("MED008", "Salbutamol", "Ventolin", 45, "2026-03-10", 28.90, "Asthma Relief", "Salbutamol", "Bronchodilator"));
        medicines.add(new Medicine("MED009", "Sertraline", "Zoloft", 20, "2026-04-05", 45.60, "Depression Treatment", "Sertraline", "SSRI"));
        medicines.add(new Medicine("MED010", "Atorvastatin", "Lipitor", 30, "2026-05-20", 58.90, "Cholesterol Management", "Atorvastatin", "Statin"));
        medicines.add(new Medicine("MED011", "Aspirin", "Bayer", 8, "2026-06-15", 6.50, "Pain Relief", "Acetylsalicylic Acid", "NSAID"));
        medicines.add(new Medicine("MED012", "Insulin", "Humalog", 12, "2026-07-30", 120.00, "Diabetes Management", "Insulin Lispro", "Insulin"));
        medicines.add(new Medicine("MED013", "Loratadine", "Claritin", 18, "2026-08-25", 16.80, "Allergy Relief", "Loratadine", "Antihistamine"));
        medicines.add(new Medicine("MED014", "Lisinopril", "Zestril", 22, "2026-09-10", 28.40, "Hypertension", "Lisinopril", "ACE Inhibitor"));
        medicines.add(new Medicine("MED015", "Fluoxetine", "Prozac", 15, "2026-10-05", 42.30, "Depression Treatment", "Fluoxetine", "SSRI"));
        medicines.add(new Medicine("MED016", "Simvastatin", "Zocor", 25, "2026-11-20", 35.70, "Cholesterol Management", "Simvastatin", "Statin"));
        medicines.add(new Medicine("MED017", "Montelukast", "Singulair", 12, "2026-12-15", 38.90, "Asthma Prevention", "Montelukast", "Leukotriene Receptor Antagonist"));
        medicines.add(new Medicine("MED018", "Pantoprazole", "Protonix", 20, "2027-01-10", 31.20, "Acid Reflux", "Pantoprazole", "Proton Pump Inhibitor"));
        medicines.add(new Medicine("MED019", "Duloxetine", "Cymbalta", 10, "2027-02-05", 67.80, "Depression Treatment", "Duloxetine", "SNRI"));
        medicines.add(new Medicine("MED020", "Losartan", "Cozaar", 28, "2027-03-20", 29.60, "Hypertension", "Losartan", "Angiotensin Receptor Blocker"));
        medicines.add(new Medicine("MED021", "Paracetamol", "Panadol Active", 5, "2025-12-31", 8.50, "Pain Relief", "Paracetamol", "Analgesic"));
        medicines.add(new Medicine("MED022", "Cephalexin", "Keflex", 35, "2026-04-15", 18.90, "Antibiotic", "Cephalexin", "Antibiotic"));
        medicines.add(new Medicine("MED023", "Diclofenac", "Voltaren", 42, "2026-05-20", 14.70, "Pain Relief", "Diclofenac", "NSAID"));
        medicines.add(new Medicine("MED024", "Ranitidine", "Zantac", 38, "2026-06-25", 19.80, "Acid Reflux", "Ranitidine", "H2 Receptor Antagonist"));
        medicines.add(new Medicine("MED025", "Fexofenadine", "Allegra", 28, "2026-07-30", 21.50, "Allergy Relief", "Fexofenadine", "Antihistamine"));
        medicines.add(new Medicine("MED026", "Glipizide", "Glucotrol", 33, "2026-08-10", 26.80, "Diabetes Management", "Glipizide", "Sulfonylurea"));
        medicines.add(new Medicine("MED027", "Nifedipine", "Adalat", 29, "2026-09-15", 35.40, "Hypertension", "Nifedipine", "Calcium Channel Blocker"));
        medicines.add(new Medicine("MED028", "Budesonide", "Pulmicort", 24, "2026-10-20", 52.30, "Asthma Prevention", "Budesonide", "Corticosteroid"));
        medicines.add(new Medicine("MED029", "Escitalopram", "Lexapro", 18, "2026-11-25", 48.90, "Depression Treatment", "Escitalopram", "SSRI"));
        medicines.add(new Medicine("MED030", "Rosuvastatin", "Crestor", 31, "2026-12-30", 62.70, "Cholesterol Management", "Rosuvastatin", "Statin"));
        medicines.add(new Medicine("MED031", "Naproxen", "Aleve", 26, "2027-01-15", 11.40, "Pain Relief", "Naproxen", "NSAID"));
        medicines.add(new Medicine("MED032", "Metoprolol", "Lopressor", 37, "2027-02-20", 24.60, "Hypertension", "Metoprolol", "Beta Blocker"));
        medicines.add(new Medicine("MED033", "Prednisone", "Deltasone", 22, "2027-03-25", 15.80, "Anti-inflammatory", "Prednisone", "Corticosteroid"));
        medicines.add(new Medicine("MED034", "Tramadol", "Ultram", 19, "2027-04-30", 28.70, "Pain Relief", "Tramadol", "Opioid Analgesic"));
        medicines.add(new Medicine("MED035", "Warfarin", "Coumadin", 16, "2027-05-15", 12.30, "Blood Thinner", "Warfarin", "Anticoagulant"));
        medicines.add(new Medicine("MED036", "Levothyroxine", "Synthroid", 41, "2027-06-20", 18.90, "Thyroid Hormone", "Levothyroxine", "Hormone"));
        medicines.add(new Medicine("MED037", "Hydrochlorothiazide", "Microzide", 34, "2027-07-25", 16.50, "Diuretic", "Hydrochlorothiazide", "Thiazide Diuretic"));
        medicines.add(new Medicine("MED038", "Alprazolam", "Xanax", 13, "2027-08-30", 22.80, "Anxiety Treatment", "Alprazolam", "Benzodiazepine"));
        medicines.add(new Medicine("MED039", "Gabapentin", "Neurontin", 27, "2027-09-15", 31.40, "Nerve Pain", "Gabapentin", "Anticonvulsant"));
        medicines.add(new Medicine("MED040", "Clonazepam", "Klonopin", 15, "2027-10-20", 19.60, "Seizure Control", "Clonazepam", "Benzodiazepine"));

        return medicines;
    }

    public static SetAndQueueInterface<Patient> initializeSamplePatients() {
        SetAndQueueInterface<Patient> patients = new SetQueueArray<>();

        patients.add(new Patient(1, "Ahmad bin Abdullah", 3, "Male", "Penicillin", "**********", "123 Jalan Tunku Abdul Rahman, Kuala Lumpur", "01-07-2025", "Fever", "Active"));
        patients.add(new Patient(2, "Siti binti Mohamed", 7, "Female", "Paracetamol", "**********", "456 Jalan Sultan Ismail, Petaling Jaya", "02-07-2025", "Common Cold", "Active"));
        patients.add(new Patient(3, "Raj a/l Kumar", 12, "Male", "Sulfa", "**********", "789 Jalan Bukit Bintang, Kuala Lumpur", "03-07-2025", "Asthma", "Active"));
        patients.add(new Patient(4, "Lim Siew Mei", 15, "Female", "None", "**********", "321 Jalan Ampang, Kuala Lumpur", "04-07-2025", "None", "Active"));
        patients.add(new Patient(5, "Tan Ah Kow", 17, "Male", "Codeine", "**********", "654 Jalan Pudu, Kuala Lumpur", "05-07-2025", "Sports Injury", "Active"));
        patients.add(new Patient(6, "Nurul Huda binti Ismail", 22, "Female", "Latex", "**********", "987 Jalan Cheras, Kuala Lumpur", "06-07-2025", "Migraine", "Active"));
        patients.add(new Patient(7, "Krishnan a/l Muthu", 25, "Male", "None", "**********", "147 Jalan Klang Lama, Kuala Lumpur", "07-07-2025", "Depression", "Active"));
        patients.add(new Patient(8, "Wong Mei Ling", 28, "Female", "Iodine", "**********", "258 Jalan Ipoh, Kuala Lumpur", "08-07-2025", "Thyroid Disorder", "Active"));
        patients.add(new Patient(9, "Mohamed Ali bin Hassan", 31, "Male", "None", "**********", "369 Jalan Gombak, Kuala Lumpur", "09-07-2025", "Anxiety", "Active"));
        patients.add(new Patient(10, "Cheah Siew Fong", 35, "Female", "Shellfish", "**********", "741 Jalan Damansara, Petaling Jaya", "10-07-2025", "None", "Active"));
        patients.add(new Patient(11, "Arun a/l Subramaniam", 38, "Male", "None", "**********", "852 Jalan Bangsar, Kuala Lumpur", "11-07-2025", "High Cholesterol", "Active"));
        patients.add(new Patient(12, "Fatimah binti Omar", 42, "Female", "Peanuts", "**********", "963 Jalan TAR, Kuala Lumpur", "12-07-2025", "None", "Active"));
        patients.add(new Patient(13, "Lee Chong Wei", 45, "Male", "None", "**********", "159 Jalan Imbi, Kuala Lumpur", "13-07-2025", "Sleep Apnea", "Active"));
        patients.add(new Patient(14, "Aisha binti Yusof", 48, "Female", "None", "**********", "357 Jalan Raja Chulan, Kuala Lumpur", "14-07-2025", "None", "Active"));
        patients.add(new Patient(15, "Gan Eng Seng", 52, "Male", "Aspirin", "**********", "486 Jalan Tuanku Abdul Rahman, Kuala Lumpur", "15-07-2025", "Gout", "Active"));
        patients.add(new Patient(16, "Zainab binti Ahmad", 55, "Female", "None", "**********", "753 Jalan Raja Laut, Kuala Lumpur", "16-07-2025", "Hypertension", "Active"));
        patients.add(new Patient(17, "Kumar a/l Rajendran", 58, "Male", "Sulfa", "**********", "951 Jalan Sultan, Kuala Lumpur", "17-07-2025", "Diabetes", "Active"));
        patients.add(new Patient(18, "Chan Mei Lin", 62, "Female", "Latex", "**********", "357 Jalan Pahang, Kuala Lumpur", "18-07-2025", "Asthma", "Active"));
        patients.add(new Patient(19, "Ismail bin Omar", 65, "Male", "None", "**********", "159 Jalan Masjid India, Kuala Lumpur", "19-07-2025", "Heart Disease", "Active"));
        patients.add(new Patient(20, "Priya a/p Ramasamy", 68, "Female", "Penicillin", "**********", "753 Jalan Petaling, Kuala Lumpur", "20-07-2025", "Migraine", "Active"));
        patients.add(new Patient(21, "Ong Teck Seng", 72, "Male", "None", "**********", "951 Jalan Chow Kit, Kuala Lumpur", "21-07-2025", "Depression", "Active"));
        patients.add(new Patient(22, "Noraini binti Zainal", 75, "Female", "Iodine", "**********", "357 Jalan Tun Perak, Kuala Lumpur", "22-07-2025", "Thyroid Disorder", "Active"));
        patients.add(new Patient(23, "Muthu a/l Velu", 78, "Male", "None", "**********", "159 Jalan Dang Wangi, Kuala Lumpur", "23-07-2025", "Anxiety", "Active"));
        patients.add(new Patient(24, "Lau Siew Mei", 82, "Female", "Shellfish", "**********", "753 Jalan Tun Razak, Kuala Lumpur", "24-07-2025", "None", "Active"));
        patients.add(new Patient(25, "Hassan bin Ali", 85, "Male", "None", "**********", "951 Jalan Ampang, Kuala Lumpur", "25-07-2025", "High Cholesterol", "Active"));
        patients.add(new Patient(26, "Aminah binti Rashid", 88, "Female", "Aspirin", "**********", "123 Jalan Sentul, Kuala Lumpur", "26-07-2025", "Arthritis", "Active"));
        patients.add(new Patient(27, "Ravi a/l Shankar", 92, "Male", "None", "**********", "456 Jalan Kepong, Kuala Lumpur", "27-07-2025", "Hypertension", "Active"));
        patients.add(new Patient(28, "Lily Tan Mei Hua", 95, "Female", "Shellfish", "**********", "789 Jalan Setapak, Kuala Lumpur", "28-07-2025", "Allergic Rhinitis", "Active"));
        patients.add(new Patient(29, "Azman bin Yusof", 19, "Male", "Penicillin", "**********", "321 Jalan Wangsa Maju, Kuala Lumpur", "29-07-2025", "Diabetes", "Active"));
        patients.add(new Patient(30, "Grace Lim Soo Cheng", 23, "Female", "None", "**********", "654 Jalan Segambut, Kuala Lumpur", "30-07-2025", "Migraine", "Active"));
        patients.add(new Patient(31, "Suresh a/l Krishnan", 26, "Male", "Iodine", "**********", "987 Jalan Batu Caves, Selangor", "31-07-2025", "Thyroid Disorder", "Active"));
        patients.add(new Patient(32, "Farah binti Kamal", 29, "Female", "Latex", "**********", "147 Jalan Rawang, Selangor", "01-08-2025", "Asthma", "Active"));
        patients.add(new Patient(33, "Danny Ng Wei Ming", 33, "Male", "None", "**********", "258 Jalan Kajang, Selangor", "02-08-2025", "Sleep Apnea", "Active"));
        patients.add(new Patient(34, "Khadijah binti Hassan", 36, "Female", "Sulfa", "**********", "369 Jalan Selayang, Selangor", "03-08-2025", "Depression", "Active"));
        patients.add(new Patient(35, "Vincent Loh Chee Keong", 39, "Male", "None", "**********", "741 Jalan Subang, Selangor", "04-08-2025", "High Cholesterol", "Active"));
        patients.add(new Patient(36, "Rohani binti Ibrahim", 43, "Female", "Peanuts", "**********", "852 Jalan Shah Alam, Selangor", "05-08-2025", "Heart Disease", "Active"));
        patients.add(new Patient(37, "Prakash a/l Devi", 47, "Male", "None", "**********", "963 Jalan Klang, Selangor", "06-08-2025", "Anxiety", "Active"));
        patients.add(new Patient(38, "Michelle Wong Ai Ling", 51, "Female", "Codeine", "**********", "159 Jalan Puchong, Selangor", "07-08-2025", "Chronic Pain", "Active"));
        patients.add(new Patient(39, "Hafiz bin Rahman", 54, "Male", "None", "**********", "357 Jalan Cyberjaya, Selangor", "08-08-2025", "Diabetes", "Active"));
        patients.add(new Patient(40, "Stephanie Tan Li Ying", 57, "Female", "Shellfish", "**********", "753 Jalan Putrajaya, Putrajaya", "09-08-2025", "Allergic Dermatitis", "Active"));

        return patients;
    }

    public static SetAndQueueInterface<Doctor> initializeSampleDoctors() {
        SetAndQueueInterface<Doctor> doctors = new SetQueueArray<>();

        doctors.add(new Doctor("DOC001", "Dr. Sarah Chen Mei Ling", "Cardiology", "**********", "<EMAIL>", true, "Mon-Wed 9AM-5PM", false, "", ""));
        doctors.add(new Doctor("DOC002", "Dr. Robert Kim Ah Kow", "Pediatrics", "**********", "<EMAIL>", true, "Tue-Thu 8AM-4PM", false, "", ""));
        doctors.add(new Doctor("DOC003", "Dr. Lisa Wong Siew Mei", "Neurology", "**********", "<EMAIL>", true, "Wed-Fri 10AM-6PM", false, "", ""));
        doctors.add(new Doctor("DOC004", "Dr. James Lee Chong Wei", "Orthopedics", "**********", "<EMAIL>", false, "Mon-Fri 9AM-5PM", true, "15-07-2025", "20-07-2025"));
        doctors.add(new Doctor("DOC005", "Dr. Maria Garcia binti Abdullah", "Endocrinology", "**********", "<EMAIL>", true, "Mon-Fri 8AM-4PM", false, "", ""));
        doctors.add(new Doctor("DOC006", "Dr. David Wilson a/l Kumar", "Psychiatry", "0123456785", "<EMAIL>", true, "Tue-Sat 9AM-5PM", false, "", ""));
        doctors.add(new Doctor("DOC007", "Dr. Jennifer Brown Mei Fong", "Dermatology", "0123456786", "<EMAIL>", true, "Mon-Thu 10AM-6PM", false, "", ""));
        doctors.add(new Doctor("DOC008", "Dr. Michael Taylor bin Mohamed", "Emergency Medicine", "0123456787", "<EMAIL>", true, "24/7 Shifts", false, "", ""));
        doctors.add(new Doctor("DOC009", "Dr. Amanda Lim Siew Lin", "Oncology", "0123456788", "<EMAIL>", true, "Mon-Fri 9AM-5PM", false, "", ""));
        doctors.add(new Doctor("DOC010", "Dr. Christopher Tan Ah Beng", "Radiology", "**********", "<EMAIL>", true, "Mon-Fri 8AM-4PM", false, "", ""));
        doctors.add(new Doctor("DOC011", "Dr. Emily Wong Mei Ling", "Obstetrics & Gynecology", "**********", "<EMAIL>", true, "Tue-Sat 9AM-5PM", false, "", ""));
        doctors.add(new Doctor("DOC012", "Dr. Benjamin Raj a/l Kumar", "Urology", "**********", "<EMAIL>", true, "Mon-Thu 10AM-6PM", false, "", ""));
        doctors.add(new Doctor("DOC013", "Dr. Rachel Lim Hui Ying", "Internal Medicine", "**********", "<EMAIL>", true, "Mon-Fri 9AM-5PM", false, "", ""));
        doctors.add(new Doctor("DOC014", "Dr. Kevin Tan Wei Jie", "Gastroenterology", "**********", "<EMAIL>", true, "Tue-Sat 8AM-4PM", false, "", ""));
        doctors.add(new Doctor("DOC015", "Dr. Priya Sharma a/p Ravi", "Rheumatology", "**********", "<EMAIL>", true, "Wed-Fri 10AM-6PM", false, "", ""));
        doctors.add(new Doctor("DOC016", "Dr. Ahmad Farid bin Hassan", "Pulmonology", "**********", "<EMAIL>", false, "Mon-Thu 9AM-5PM", true, "10-08-2025", "15-08-2025"));
        doctors.add(new Doctor("DOC017", "Dr. Catherine Ng Siew Lan", "Nephrology", "**********", "<EMAIL>", true, "Mon-Fri 8AM-4PM", false, "", ""));
        doctors.add(new Doctor("DOC018", "Dr. Rajesh Kumar a/l Devi", "Hematology", "**********", "<EMAIL>", true, "Tue-Thu 9AM-5PM", false, "", ""));
        doctors.add(new Doctor("DOC019", "Dr. Melissa Chan Ai Ling", "Infectious Disease", "**********", "<EMAIL>", true, "Wed-Sat 10AM-6PM", false, "", ""));
        doctors.add(new Doctor("DOC020", "Dr. Hafiz Ismail bin Omar", "Anesthesiology", "**********", "<EMAIL>", true, "24/7 On-Call", false, "", ""));

        return doctors;
    }

    public static SetAndQueueInterface<Consultation> initializeSampleConsultations() {
        SetAndQueueInterface<Consultation> consultations = new SetQueueArray<>();

        consultations.add(new Consultation("CON001", "1", "DOC001", "10-07-2025", "Completed", "Patient shows signs of hypertension"));
        consultations.add(new Consultation("CON002", "2", "DOC002", "12-07-2025", "Completed", "Regular checkup"));
        consultations.add(new Consultation("CON003", "3", "DOC003", "15-07-2025", "Completed", "Neurological examination completed"));
        consultations.add(new Consultation("CON004", "4", "DOC005", "16-07-2025", "Completed", "Diabetes management consultation"));
        consultations.add(new Consultation("CON005", "5", "DOC001", "17-07-2025", "Completed", "Cardiac assessment"));
        consultations.add(new Consultation("CON006", "6", "DOC006", "18-07-2025", "Completed", "Mental health evaluation"));
        consultations.add(new Consultation("CON007", "7", "DOC007", "19-07-2025", "Completed", "Skin condition review"));
        consultations.add(new Consultation("CON008", "8", "DOC002", "20-07-2025", "Completed", "Pediatric consultation"));
        consultations.add(new Consultation("CON009", "9", "DOC006", "21-07-2025", "Completed", "Anxiety treatment"));
        consultations.add(new Consultation("CON010", "10", "DOC005", "21-07-2025", "Completed", "Thyroid function test review"));
        consultations.add(new Consultation("CON011", "11", "DOC001", "23-07-2025", "Completed", "Cholesterol management"));
        consultations.add(new Consultation("CON012", "12", "DOC003", "24-07-2025", "Completed", "Neurological examination"));
        consultations.add(new Consultation("CON013", "13", "DOC007", "25-07-2025", "Completed", "Dermatological examination"));
        consultations.add(new Consultation("CON014", "14", "DOC008", "26-07-2025", "Completed", "Emergency consultation"));
        consultations.add(new Consultation("CON015", "15", "DOC009", "27-07-2025", "Completed", "Oncology consultation"));
        consultations.add(new Consultation("CON016", "16", "DOC010", "28-07-2025", "Completed", "Radiological assessment"));
        consultations.add(new Consultation("CON017", "17", "DOC011", "29-07-2025", "Completed", "Gynecological examination"));
        consultations.add(new Consultation("CON018", "18", "DOC012", "30-07-2025", "Completed", "Urological consultation"));
        consultations.add(new Consultation("CON019", "19", "DOC001", "31-07-2025", "Completed", "Cardiac examination"));
        consultations.add(new Consultation("CON020", "20", "DOC006", "01-08-2025", "Completed", "Psychiatric evaluation"));
        consultations.add(new Consultation("CON021", "21", "DOC013", "02-08-2025", "Completed", "Internal medicine consultation"));
        consultations.add(new Consultation("CON022", "22", "DOC014", "03-08-2025", "Completed", "Gastroenterology examination"));
        consultations.add(new Consultation("CON023", "23", "DOC015", "04-08-2025", "Completed", "Rheumatology assessment"));
        consultations.add(new Consultation("CON024", "24", "DOC017", "05-08-2025", "Completed", "Nephrology consultation"));
        consultations.add(new Consultation("CON025", "25", "DOC018", "06-08-2025", "Completed", "Hematology examination"));
        consultations.add(new Consultation("CON026", "26", "DOC019", "07-08-2025", "Completed", "Infectious disease consultation"));
        consultations.add(new Consultation("CON027", "27", "DOC001", "08-08-2025", "Completed", "Follow-up cardiac assessment"));
        consultations.add(new Consultation("CON028", "28", "DOC007", "09-08-2025", "Completed", "Dermatology follow-up"));
        consultations.add(new Consultation("CON029", "29", "DOC005", "10-08-2025", "Completed", "Endocrinology consultation"));
        consultations.add(new Consultation("CON030", "30", "DOC013", "11-08-2025", "Completed", "General health checkup"));
        consultations.add(new Consultation("CON031", "31", "DOC006", "12-08-2025", "Completed", "Mental health follow-up"));
        consultations.add(new Consultation("CON032", "32", "DOC002", "13-08-2025", "Completed", "Pediatric vaccination"));
        consultations.add(new Consultation("CON033", "33", "DOC008", "14-08-2025", "Completed", "Emergency treatment"));
        consultations.add(new Consultation("CON034", "34", "DOC014", "15-08-2025", "Completed", "Digestive system examination"));
        consultations.add(new Consultation("CON035", "35", "DOC015", "16-08-2025", "Completed", "Joint pain assessment"));
        consultations.add(new Consultation("CON036", "36", "DOC001", "17-08-2025", "Completed", "Cardiovascular follow-up examination"));
        consultations.add(new Consultation("CON037", "37", "DOC002", "18-08-2025", "Completed", "Pediatric growth assessment"));
        consultations.add(new Consultation("CON038", "38", "DOC003", "19-08-2025", "Completed", "Neurological screening for headaches"));
        consultations.add(new Consultation("CON039", "39", "DOC004", "20-08-2025", "Completed", "Orthopedic consultation for back pain"));
        consultations.add(new Consultation("CON040", "40", "DOC005", "21-08-2025", "Completed", "Endocrine system evaluation"));
        consultations.add(new Consultation("CON041", "1", "DOC006", "22-08-2025", "Completed", "Mental health follow-up session"));
        consultations.add(new Consultation("CON042", "1", "DOC007", "23-08-2025", "Completed", "Dermatological skin check"));
        consultations.add(new Consultation("CON043", "1", "DOC008", "24-08-2025", "Completed", "Emergency medicine consultation"));
        consultations.add(new Consultation("CON044", "1", "DOC009", "25-08-2025", "Completed", "Oncology screening appointment"));
        consultations.add(new Consultation("CON045", "2", "DOC010", "26-08-2025", "Completed", "Radiological examination review"));
        consultations.add(new Consultation("CON046", "2", "DOC011", "27-08-2025", "Completed", "Gynecological wellness check"));
        consultations.add(new Consultation("CON047", "2", "DOC012", "28-08-2025", "Completed", "Urological assessment"));
        consultations.add(new Consultation("CON048", "2", "DOC013", "29-08-2025", "Completed", "Internal medicine consultation"));
        consultations.add(new Consultation("CON049", "3", "DOC014", "30-08-2025", "Completed", "Gastroenterological examination"));
        consultations.add(new Consultation("CON050", "3", "DOC015", "31-08-2025", "Completed", "Rheumatological joint assessment"));
        consultations.add(new Consultation("CON051", "3", "DOC016", "01-09-2025", "Completed", "Pulmonology breathing test"));
        consultations.add(new Consultation("CON052", "3", "DOC017", "02-09-2025", "Completed", "Nephrology kidney function test"));
        consultations.add(new Consultation("CON053", "4", "DOC018", "03-09-2025", "Completed", "Hematology blood work review"));
        consultations.add(new Consultation("CON054", "4", "DOC019", "04-09-2025", "Completed", "Infectious disease consultation"));
        consultations.add(new Consultation("CON055", "4", "DOC020", "05-09-2025", "Completed", "Anesthesiology pre-operative assessment"));
        consultations.add(new Consultation("CON056", "4", "DOC001", "06-09-2025", "Completed", "Cardiac stress test evaluation"));
        consultations.add(new Consultation("CON057", "3", "DOC002", "07-09-2025", "Completed", "Pediatric immunization consultation"));
        consultations.add(new Consultation("CON058", "8", "DOC003", "08-09-2025", "Completed", "Neurological cognitive assessment"));
        consultations.add(new Consultation("CON059", "15", "DOC005", "09-09-2025", "Completed", "Diabetes management review"));
        consultations.add(new Consultation("CON060", "20", "DOC006", "10-09-2025", "Completed", "Psychiatric medication review"));
        consultations.add(new Consultation("CON061", "6", "DOC007", "11-09-2025", "Completed", "Dermatology mole examination"));
        consultations.add(new Consultation("CON062", "9", "DOC008", "12-09-2025", "Completed", "Emergency trauma assessment"));
        consultations.add(new Consultation("CON063", "14", "DOC009", "13-09-2025", "Completed", "Cancer follow-up consultation"));
        consultations.add(new Consultation("CON064", "16", "DOC010", "14-09-2025", "Completed", "Imaging results discussion"));
        consultations.add(new Consultation("CON065", "7", "DOC011", "15-09-2025", "Completed", "Prenatal care consultation"));
        consultations.add(new Consultation("CON066", "7", "DOC012", "16-09-2025", "Completed", "Prostate examination"));
        consultations.add(new Consultation("CON067", "8", "DOC013", "17-09-2025", "Completed", "General health screening"));
        consultations.add(new Consultation("CON068", "9", "DOC014", "18-09-2025", "Completed", "Digestive health consultation"));
        consultations.add(new Consultation("CON069", "5", "DOC015", "19-09-2025", "Completed", "Arthritis management review"));
        consultations.add(new Consultation("CON070", "5", "DOC016", "20-09-2025", "Completed", "Respiratory function assessment"));
        consultations.add(new Consultation("CON071", "5", "DOC017", "21-09-2025", "Completed", "Kidney health evaluation"));
        consultations.add(new Consultation("CON072", "5", "DOC018", "22-09-2025", "Completed", "Blood disorder consultation"));
        consultations.add(new Consultation("CON073", "6", "DOC019", "23-09-2025", "Completed", "Infection treatment follow-up"));
        consultations.add(new Consultation("CON074", "6", "DOC020", "24-09-2025", "Completed", "Pain management consultation"));
        consultations.add(new Consultation("CON075", "6", "DOC001", "25-09-2025", "Completed", "Heart rhythm monitoring"));
        consultations.add(new Consultation("CON076", "6", "DOC002", "26-09-2025", "Completed", "Child development assessment"));
        consultations.add(new Consultation("CON077", "2", "DOC003", "27-09-2025", "Completed", "Memory and cognition test"));
        consultations.add(new Consultation("CON078", "7", "DOC005", "28-09-2025", "Completed", "Thyroid function evaluation"));
        consultations.add(new Consultation("CON079", "11", "DOC006", "29-09-2025", "Completed", "Anxiety disorder treatment"));
        consultations.add(new Consultation("CON080", "18", "DOC007", "30-09-2025", "Completed", "Skin cancer screening"));
        consultations.add(new Consultation("CON081", "1", "DOC008", "01-10-2025", "Completed", "Emergency chest pain evaluation"));
        consultations.add(new Consultation("CON082", "4", "DOC009", "02-10-2025", "Completed", "Tumor marker assessment"));
        consultations.add(new Consultation("CON083", "23", "DOC010", "03-10-2025", "Completed", "X-ray interpretation session"));
        consultations.add(new Consultation("CON084", "1", "DOC011", "04-10-2025", "Completed", "Reproductive health consultation"));
        consultations.add(new Consultation("CON085", "5", "DOC012", "05-10-2025", "Completed", "Bladder health assessment"));
        consultations.add(new Consultation("CON086", "24", "DOC013", "06-10-2025", "Completed", "Preventive medicine consultation"));
        consultations.add(new Consultation("CON087", "26", "DOC014", "07-10-2025", "Completed", "Liver function evaluation"));
        consultations.add(new Consultation("CON088", "5", "DOC015", "08-10-2025", "Completed", "Joint mobility assessment"));
        consultations.add(new Consultation("CON089", "10", "DOC016", "09-10-2025", "Completed", "Asthma control evaluation"));
        consultations.add(new Consultation("CON090", "10", "DOC017", "10-10-2025", "Completed", "Dialysis consultation"));
        consultations.add(new Consultation("CON091", "10", "DOC018", "11-10-2025", "Completed", "Anemia treatment review"));
        consultations.add(new Consultation("CON092", "10", "DOC019", "12-10-2025", "Completed", "Vaccination consultation"));
        consultations.add(new Consultation("CON093", "11", "DOC020", "13-10-2025", "Completed", "Surgical consultation"));
        consultations.add(new Consultation("CON094", "11", "DOC001", "14-10-2025", "Completed", "Hypertension medication adjustment"));
        consultations.add(new Consultation("CON095", "11", "DOC002", "15-10-2025", "Completed", "Growth hormone assessment"));
        consultations.add(new Consultation("CON096", "11", "DOC003", "16-10-2025", "Completed", "Seizure disorder evaluation"));
        consultations.add(new Consultation("CON097", "3", "DOC005", "17-10-2025", "Completed", "Insulin therapy consultation"));
        consultations.add(new Consultation("CON098", "8", "DOC006", "18-10-2025", "Completed", "Depression therapy session"));
        consultations.add(new Consultation("CON099", "15", "DOC007", "19-10-2025", "Completed", "Acne treatment consultation"));
        consultations.add(new Consultation("CON100", "20", "DOC008", "20-10-2025", "Completed", "Accident injury assessment"));
        consultations.add(new Consultation("CON101", "1", "DOC009", "21-10-2025", "Completed", "Chemotherapy consultation"));
        consultations.add(new Consultation("CON102", "3", "DOC010", "22-10-2025", "Completed", "MRI scan review"));
        consultations.add(new Consultation("CON103", "32", "DOC011", "23-10-2025", "Completed", "Menopause management"));
        consultations.add(new Consultation("CON104", "1", "DOC012", "24-10-2025", "Completed", "Kidney stone consultation"));
        consultations.add(new Consultation("CON105", "12", "DOC013", "25-10-2025", "Completed", "Cholesterol management"));
        consultations.add(new Consultation("CON106", "12", "DOC014", "26-10-2025", "Completed", "Inflammatory bowel disease"));
        consultations.add(new Consultation("CON107", "12", "DOC015", "27-10-2025", "Completed", "Osteoporosis screening"));
        consultations.add(new Consultation("CON108", "12", "DOC016", "28-10-2025", "Completed", "COPD management review"));
        consultations.add(new Consultation("CON109", "5", "DOC017", "29-10-2025", "Completed", "Chronic kidney disease"));
        consultations.add(new Consultation("CON110", "13", "DOC018", "30-10-2025", "Completed", "Leukemia follow-up"));
        consultations.add(new Consultation("CON111", "14", "DOC019", "31-10-2025", "Completed", "Hepatitis screening"));
        consultations.add(new Consultation("CON112", "5", "DOC020", "01-11-2025", "Completed", "Post-operative check"));
        consultations.add(new Consultation("CON113", "12", "DOC001", "02-11-2025", "Completed", "Arrhythmia monitoring"));
        consultations.add(new Consultation("CON114", "15", "DOC002", "03-11-2025", "Completed", "Developmental milestone check"));
        consultations.add(new Consultation("CON115", "16", "DOC003", "04-11-2025", "Completed", "Parkinson's disease assessment"));
        consultations.add(new Consultation("CON116", "12", "DOC005", "05-11-2025", "Completed", "Adrenal function test"));
        consultations.add(new Consultation("CON117", "2", "DOC006", "06-11-2025", "Completed", "Bipolar disorder management"));
        consultations.add(new Consultation("CON118", "7", "DOC007", "07-11-2025", "Completed", "Psoriasis treatment review"));
        consultations.add(new Consultation("CON119", "11", "DOC008", "08-11-2025", "Completed", "Cardiac arrest consultation"));
        consultations.add(new Consultation("CON120", "18", "DOC009", "09-11-2025", "Completed", "Radiation therapy planning"));
        consultations.add(new Consultation("CON121", "1", "DOC010", "10-11-2025", "Completed", "CT scan interpretation"));
        consultations.add(new Consultation("CON122", "1", "DOC011", "11-11-2025", "Completed", "Fertility consultation"));
        consultations.add(new Consultation("CON123", "1", "DOC012", "12-11-2025", "Completed", "Erectile dysfunction treatment"));
        consultations.add(new Consultation("CON124", "1", "DOC013", "13-11-2025", "Completed", "Metabolic syndrome evaluation"));
        consultations.add(new Consultation("CON125", "5", "DOC014", "14-11-2025", "Completed", "Peptic ulcer management"));
        consultations.add(new Consultation("CON126", "5", "DOC015", "15-11-2025", "Completed", "Fibromyalgia consultation"));
        consultations.add(new Consultation("CON127", "5", "DOC016", "16-11-2025", "Completed", "Sleep apnea evaluation"));
        consultations.add(new Consultation("CON128", "5", "DOC017", "17-11-2025", "Completed", "Hypertensive nephropathy"));
        consultations.add(new Consultation("CON129", "17", "DOC018", "18-11-2025", "Completed", "Thrombocytopenia treatment"));
        consultations.add(new Consultation("CON130", "18", "DOC019", "19-11-2025", "Completed", "Antibiotic resistance consultation"));
        consultations.add(new Consultation("CON131", "19", "DOC020", "20-11-2025", "Completed", "Chronic pain management"));
        consultations.add(new Consultation("CON132", "20", "DOC001", "21-11-2025", "Completed", "Valve replacement follow-up"));
        consultations.add(new Consultation("CON133", "21", "DOC002", "22-11-2025", "Completed", "Autism spectrum screening"));
        consultations.add(new Consultation("CON134", "22", "DOC003", "23-11-2025", "Completed", "Multiple sclerosis evaluation"));
        consultations.add(new Consultation("CON135", "23", "DOC005", "24-11-2025", "Completed", "Pituitary gland assessment"));
        consultations.add(new Consultation("CON136", "24", "DOC006", "25-11-2025", "Completed", "PTSD therapy session"));
        consultations.add(new Consultation("CON137", "11", "DOC007", "26-11-2025", "Completed", "Melanoma screening"));
        consultations.add(new Consultation("CON138", "8", "DOC008", "27-11-2025", "Completed", "Stroke rehabilitation"));
        consultations.add(new Consultation("CON139", "15", "DOC009", "28-11-2025", "Completed", "Immunotherapy consultation"));
        consultations.add(new Consultation("CON140", "20", "DOC010", "29-11-2025", "Completed", "Ultrasound examination"));
        consultations.add(new Consultation("CON141", "1", "DOC011", "30-11-2025", "Completed", "Endometriosis management"));
        consultations.add(new Consultation("CON142", "1", "DOC012", "01-12-2025", "Completed", "Benign prostatic hyperplasia"));
        consultations.add(new Consultation("CON143", "1", "DOC013", "02-12-2025", "Completed", "Vitamin deficiency assessment"));
        consultations.add(new Consultation("CON144", "1", "DOC014", "03-12-2025", "Completed", "Gallbladder disease consultation"));
        consultations.add(new Consultation("CON145", "25", "DOC015", "04-12-2025", "Completed", "Lupus management review"));
        consultations.add(new Consultation("CON146", "26", "DOC016", "05-12-2025", "Completed", "Pulmonary embolism treatment"));
        consultations.add(new Consultation("CON147", "27", "DOC017", "06-12-2025", "Completed", "Polycystic kidney disease"));
        consultations.add(new Consultation("CON148", "28", "DOC018", "07-12-2025", "Completed", "Hemophilia management"));
        consultations.add(new Consultation("CON149", "29", "DOC019", "08-12-2025", "Completed", "Tuberculosis screening"));
        consultations.add(new Consultation("CON150", "30", "DOC020", "09-12-2025", "Completed", "Regional anesthesia consultation"));
        consultations.add(new Consultation("CON151", "11", "DOC001", "10-12-2025", "Completed", "Congestive heart failure"));
        consultations.add(new Consultation("CON152", "32", "DOC002", "11-12-2025", "Completed", "ADHD assessment"));
        consultations.add(new Consultation("CON153", "33", "DOC003", "12-12-2025", "Completed", "Alzheimer's disease evaluation"));
        consultations.add(new Consultation("CON154", "34", "DOC005", "13-12-2025", "Completed", "Gestational diabetes management"));
        consultations.add(new Consultation("CON155", "35", "DOC006", "14-12-2025", "Completed", "Obsessive-compulsive disorder"));
        consultations.add(new Consultation("CON156", "36", "DOC007", "15-12-2025", "Completed", "Rosacea treatment consultation"));
        consultations.add(new Consultation("CON157", "2", "DOC008", "16-12-2025", "Completed", "Anaphylaxis management"));
        consultations.add(new Consultation("CON158", "7", "DOC009", "17-12-2025", "Completed", "Bone marrow biopsy consultation"));
        consultations.add(new Consultation("CON159", "11", "DOC010", "18-12-2025", "Completed", "Nuclear medicine scan"));
        consultations.add(new Consultation("CON160", "18", "DOC011", "19-12-2025", "Completed", "Ovarian cyst management"));
        consultations.add(new Consultation("CON161", "1", "DOC012", "20-12-2025", "Completed", "Testicular cancer screening"));
        consultations.add(new Consultation("CON162", "2", "DOC013", "21-12-2025", "Completed", "Autoimmune disease consultation"));
        consultations.add(new Consultation("CON163", "3", "DOC014", "22-12-2025", "Completed", "Celiac disease management"));
        consultations.add(new Consultation("CON164", "4", "DOC015", "23-12-2025", "Completed", "Gout management review"));
        consultations.add(new Consultation("CON165", "5", "DOC016", "24-12-2025", "Completed", "Interstitial lung disease"));
        consultations.add(new Consultation("CON166", "6", "DOC017", "25-12-2025", "Completed", "Diabetic nephropathy"));
        consultations.add(new Consultation("CON167", "7", "DOC018", "26-12-2025", "Completed", "Sickle cell disease"));
        consultations.add(new Consultation("CON168", "8", "DOC019", "27-12-2025", "Completed", "HIV consultation"));
        consultations.add(new Consultation("CON169", "9", "DOC020", "28-12-2025", "Completed", "Epidural injection consultation"));
        consultations.add(new Consultation("CON170", "10", "DOC001", "29-12-2025", "Completed", "Peripheral artery disease"));
        consultations.add(new Consultation("CON171", "11", "DOC002", "30-12-2025", "Completed", "Cystic fibrosis management"));
        consultations.add(new Consultation("CON172", "2", "DOC003", "31-12-2025", "Completed", "Huntington's disease consultation"));
        consultations.add(new Consultation("CON173", "13", "DOC005", "01-01-2026", "Completed", "Polycystic ovary syndrome"));
        consultations.add(new Consultation("CON174", "14", "DOC006", "02-01-2026", "Completed", "Eating disorder consultation"));
        consultations.add(new Consultation("CON175", "15", "DOC007", "03-01-2026", "Completed", "Vitiligo treatment review"));
        consultations.add(new Consultation("CON176", "16", "DOC008", "04-01-2026", "Completed", "Burn injury assessment"));
        consultations.add(new Consultation("CON177", "17", "DOC009", "05-01-2026", "Completed", "Palliative care consultation"));
        consultations.add(new Consultation("CON178", "18", "DOC010", "06-01-2026", "Completed", "Mammography interpretation"));
        consultations.add(new Consultation("CON179", "19", "DOC011", "07-01-2026", "Completed", "Uterine fibroid consultation"));
        consultations.add(new Consultation("CON180", "20", "DOC012", "08-01-2026", "Completed", "Vasectomy consultation"));
        consultations.add(new Consultation("CON181", "21", "DOC013", "09-01-2026", "Completed", "Iron deficiency anemia"));
        consultations.add(new Consultation("CON182", "22", "DOC014", "10-01-2026", "Completed", "Hepatitis C treatment"));
        consultations.add(new Consultation("CON183", "23", "DOC015", "11-01-2026", "Completed", "Carpal tunnel syndrome"));
        consultations.add(new Consultation("CON184", "24", "DOC016", "12-01-2026", "Completed", "Chronic bronchitis"));
        consultations.add(new Consultation("CON185", "25", "DOC017", "13-01-2026", "Completed", "Acute kidney injury"));
        consultations.add(new Consultation("CON186", "26", "DOC018", "14-01-2026", "Completed", "Platelet disorder consultation"));
        consultations.add(new Consultation("CON187", "27", "DOC019", "15-01-2026", "Completed", "Malaria treatment"));
        consultations.add(new Consultation("CON188", "28", "DOC020", "16-01-2026", "Completed", "Spinal anesthesia consultation"));
        consultations.add(new Consultation("CON189", "29", "DOC001", "17-01-2026", "Completed", "Mitral valve prolapse"));
        consultations.add(new Consultation("CON190", "30", "DOC002", "18-01-2026", "Completed", "Cerebral palsy management"));
        consultations.add(new Consultation("CON191", "31", "DOC003", "19-01-2026", "Completed", "Migraine prevention consultation"));
        consultations.add(new Consultation("CON192", "32", "DOC005", "20-01-2026", "Completed", "Cushing's syndrome evaluation"));
        consultations.add(new Consultation("CON193", "33", "DOC006", "21-01-2026", "Completed", "Panic disorder treatment"));
        consultations.add(new Consultation("CON194", "34", "DOC007", "22-01-2026", "Completed", "Basal cell carcinoma"));
        consultations.add(new Consultation("CON195", "35", "DOC008", "23-01-2026", "Completed", "Hypothermia treatment"));
        consultations.add(new Consultation("CON196", "36", "DOC009", "24-01-2026", "Completed", "Lymphoma consultation"));
        consultations.add(new Consultation("CON197", "37", "DOC010", "25-01-2026", "Completed", "Bone density scan"));
        consultations.add(new Consultation("CON198", "38", "DOC011", "26-01-2026", "Completed", "Pelvic inflammatory disease"));
        consultations.add(new Consultation("CON199", "39", "DOC012", "27-01-2026", "Completed", "Penile cancer screening"));
        consultations.add(new Consultation("CON200", "40", "DOC013", "28-01-2026", "Completed", "Chronic fatigue syndrome"));
        consultations.add(new Consultation("CON201", "1", "DOC014", "29-01-2026", "Completed", "Pancreatic disorder consultation"));
        consultations.add(new Consultation("CON202", "2", "DOC015", "30-01-2026", "Completed", "Tendonitis treatment"));
        consultations.add(new Consultation("CON203", "3", "DOC016", "31-01-2026", "Completed", "Pneumothorax management"));
        consultations.add(new Consultation("CON204", "4", "DOC017", "01-02-2026", "Completed", "Glomerulonephritis consultation"));
        consultations.add(new Consultation("CON205", "5", "DOC018", "02-02-2026", "Completed", "Aplastic anemia treatment"));
        consultations.add(new Consultation("CON206", "6", "DOC019", "03-02-2026", "Completed", "Dengue fever management"));
        consultations.add(new Consultation("CON207", "7", "DOC020", "04-02-2026", "Completed", "Nerve block consultation"));
        consultations.add(new Consultation("CON208", "8", "DOC001", "05-02-2026", "Completed", "Aortic stenosis evaluation"));
        consultations.add(new Consultation("CON209", "9", "DOC002", "06-02-2026", "Completed", "Down syndrome consultation"));
        consultations.add(new Consultation("CON210", "10", "DOC003", "07-02-2026", "Completed", "Trigeminal neuralgia"));
        consultations.add(new Consultation("CON211", "11", "DOC005", "08-02-2026", "Completed", "Addison's disease management"));
        consultations.add(new Consultation("CON212", "12", "DOC006", "09-02-2026", "Completed", "Schizophrenia treatment"));
        consultations.add(new Consultation("CON213", "13", "DOC007", "10-02-2026", "Completed", "Hidradenitis suppurativa"));
        consultations.add(new Consultation("CON214", "14", "DOC008", "11-02-2026", "Completed", "Sepsis management"));
        consultations.add(new Consultation("CON215", "15", "DOC009", "12-02-2026", "Completed", "Mesothelioma consultation"));
        consultations.add(new Consultation("CON216", "16", "DOC010", "13-02-2026", "Completed", "PET scan interpretation"));
        consultations.add(new Consultation("CON217", "17", "DOC011", "14-02-2026", "Completed", "Ectopic pregnancy management"));
        consultations.add(new Consultation("CON218", "18", "DOC012", "15-02-2026", "Completed", "Hydrocele treatment"));
        consultations.add(new Consultation("CON219", "19", "DOC013", "16-02-2026", "Completed", "Osteomalacia consultation"));
        consultations.add(new Consultation("CON220", "20", "DOC014", "17-02-2026", "Completed", "Esophageal cancer screening"));

        return consultations;
    }

    public static SetAndQueueInterface<Treatment> initializeSampleTreatments() {
        SetAndQueueInterface<Treatment> treatments = new SetQueueArray<>();
        treatments.add(new Treatment("TRE001", "1", "DOC001", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE002", "2", "DOC002", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE003", "3", "DOC003", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE004", "4", "DOC004", "Allergy", "01-09-2025"));
        treatments.add(new Treatment("TRE005", "5", "DOC005", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE006", "6", "DOC006", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE007", "7", "DOC007", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE008", "8", "DOC008", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE009", "9", "DOC009", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE010", "10", "DOC010", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE011", "11", "DOC011", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE012", "12", "DOC012", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE013", "13", "DOC013", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE014", "14", "DOC014", "Allergy", "01-09-2025"));
        treatments.add(new Treatment("TRE015", "15", "DOC015", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE016", "16", "DOC016", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE017", "17", "DOC017", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE018", "18", "DOC018", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE019", "19", "DOC019", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE020", "20", "DOC020", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE021", "21", "DOC001", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE022", "22", "DOC002", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE023", "23", "DOC003", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE024", "24", "DOC004", "Allergy", "01-09-2025"));
        treatments.add(new Treatment("TRE025", "25", "DOC005", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE026", "26", "DOC006", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE027", "27", "DOC007", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE028", "28", "DOC008", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE029", "29", "DOC009", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE030", "30", "DOC010", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE031", "31", "DOC011", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE032", "32", "DOC012", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE033", "33", "DOC013", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE034", "34", "DOC014", "Allergy", "01-09-2025"));
        treatments.add(new Treatment("TRE035", "35", "DOC015", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE036", "36", "DOC016", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE037", "37", "DOC017", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE038", "38", "DOC018", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE039", "39", "DOC019", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE040", "40", "DOC020", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE041", "1", "DOC001", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE042", "2", "DOC002", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE043", "3", "DOC003", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE044", "4", "DOC004", "Allergy", "01-09-2025"));
        treatments.add(new Treatment("TRE045", "5", "DOC005", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE046", "6", "DOC006", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE047", "7", "DOC007", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE048", "8", "DOC008", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE049", "9", "DOC009", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE050", "10", "DOC010", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE051", "11", "DOC011", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE052", "12", "DOC012", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE053", "13", "DOC013", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE054", "14", "DOC014", "Allergy", "01-09-2025"));
        treatments.add(new Treatment("TRE055", "15", "DOC015", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE056", "16", "DOC016", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE057", "17", "DOC017", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE058", "18", "DOC018", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE059", "19", "DOC019", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE060", "20", "DOC020", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE061", "21", "DOC001", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE062", "22", "DOC002", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE063", "23", "DOC003", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE064", "24", "DOC004", "Allergy", "01-09-2025"));
        treatments.add(new Treatment("TRE065", "25", "DOC005", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE066", "26", "DOC006", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE067", "27", "DOC007", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE068", "28", "DOC008", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE069", "29", "DOC009", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE070", "30", "DOC010", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE071", "31", "DOC011", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE072", "32", "DOC012", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE073", "33", "DOC013", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE074", "34", "DOC014", "Allergy", "01-09-2025"));
        treatments.add(new Treatment("TRE075", "35", "DOC015", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE076", "36", "DOC016", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE077", "37", "DOC017", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE078", "38", "DOC018", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE079", "39", "DOC019", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE080", "40", "DOC020", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE081", "1", "DOC001", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE082", "2", "DOC002", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE083", "3", "DOC003", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE084", "4", "DOC004", "Allergy", "01-09-2025"));
        treatments.add(new Treatment("TRE085", "5", "DOC005", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE086", "6", "DOC006", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE087", "7", "DOC007", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE088", "8", "DOC008", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE089", "9", "DOC009", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE090", "10", "DOC010", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE091", "11", "DOC011", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE092", "12", "DOC012", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE093", "13", "DOC013", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE094", "14", "DOC014", "Allergy", "01-09-2025"));
        treatments.add(new Treatment("TRE095", "15", "DOC015", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE096", "16", "DOC016", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE097", "17", "DOC017", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE098", "18", "DOC018", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE099", "19", "DOC019", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE100", "20", "DOC020", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE101", "21", "DOC001", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE102", "22", "DOC002", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE103", "23", "DOC003", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE104", "24", "DOC004", "Allergy", "01-09-2025"));
        treatments.add(new Treatment("TRE105", "25", "DOC005", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE106", "26", "DOC006", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE107", "27", "DOC007", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE108", "28", "DOC008", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE109", "29", "DOC009", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE110", "30", "DOC010", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE111", "31", "DOC011", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE112", "32", "DOC012", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE113", "33", "DOC013", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE114", "34", "DOC014", "Allergy", "01-09-2025"));
        treatments.add(new Treatment("TRE115", "35", "DOC015", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE116", "36", "DOC016", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE117", "37", "DOC017", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE118", "38", "DOC018", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE119", "39", "DOC019", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE120", "40", "DOC020", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE121", "1", "DOC001", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE122", "2", "DOC002", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE123", "3", "DOC003", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE124", "4", "DOC004", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE125", "5", "DOC005", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE126", "6", "DOC006", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE127", "7", "DOC007", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE128", "8", "DOC008", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE129", "9", "DOC009", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE130", "10", "DOC010", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE131", "11", "DOC011", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE132", "12", "DOC012", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE133", "13", "DOC013", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE134", "14", "DOC014", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE135", "15", "DOC015", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE136", "16", "DOC016", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE137", "17", "DOC017", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE138", "18", "DOC018", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE139", "19", "DOC019", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE140", "20", "DOC020", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE141", "21", "DOC001", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE142", "22", "DOC002", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE143", "23", "DOC003", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE144", "24", "DOC004", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE145", "25", "DOC005", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE146", "26", "DOC006", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE147", "27", "DOC007", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE148", "28", "DOC008", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE149", "29", "DOC009", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE150", "30", "DOC010", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE151", "31", "DOC011", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE152", "32", "DOC012", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE153", "33", "DOC013", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE154", "34", "DOC014", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE155", "35", "DOC015", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE156", "36", "DOC016", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE157", "37", "DOC017", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE158", "38", "DOC018", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE159", "39", "DOC019", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE160", "40", "DOC020", "Diabetes", "01-09-2025"));
        treatments.add(new Treatment("TRE161", "1", "DOC001", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE162", "2", "DOC002", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE163", "3", "DOC003", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE164", "4", "DOC004", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE165", "5", "DOC005", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE166", "6", "DOC006", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE167", "7", "DOC007", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE168", "8", "DOC008", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE169", "9", "DOC009", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE170", "10", "DOC010", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE171", "11", "DOC011", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE172", "12", "DOC012", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE173", "13", "DOC013", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE174", "14", "DOC014", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE175", "15", "DOC015", "Back Pain", "01-09-2025"));
        treatments.add(new Treatment("TRE176", "16", "DOC016", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE177", "17", "DOC017", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE178", "18", "DOC018", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE179", "19", "DOC019", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE180", "20", "DOC020", "Flu", "01-09-2025"));
        treatments.add(new Treatment("TRE181", "21", "DOC001", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE182", "22", "DOC002", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE183", "23", "DOC003", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE184", "24", "DOC004", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE185", "25", "DOC005", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE186", "26", "DOC006", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE187", "27", "DOC007", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE188", "28", "DOC008", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE189", "29", "DOC009", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE190", "30", "DOC010", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE191", "31", "DOC011", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE192", "32", "DOC012", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE193", "33", "DOC013", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE194", "34", "DOC014", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE195", "35", "DOC015", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE196", "36", "DOC016", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE197", "37", "DOC017", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE198", "38", "DOC018", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE199", "39", "DOC019", "Migraine", "01-09-2025"));
        treatments.add(new Treatment("TRE200", "40", "DOC020", "Allergy", "01-09-2025"));
        treatments.add(new Treatment("TRE201", "1", "DOC001", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE202", "2", "DOC002", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE203", "3", "DOC003", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE204", "4", "DOC004", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE205", "5", "DOC005", "Hypertension", "01-09-2025"));
        treatments.add(new Treatment("TRE206", "6", "DOC006", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE207", "7", "DOC007", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE208", "8", "DOC008", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE209", "9", "DOC009", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE210", "10", "DOC010", "Asthma", "01-09-2025"));
        treatments.add(new Treatment("TRE211", "11", "DOC011", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE212", "12", "DOC012", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE213", "13", "DOC013", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE214", "14", "DOC014", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE215", "15", "DOC015", "Depression", "01-09-2025"));
        treatments.add(new Treatment("TRE216", "16", "DOC016", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE217", "17", "DOC017", "Thyroid Disorder", "01-09-2025"));
        treatments.add(new Treatment("TRE218", "18", "DOC018", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE219", "19", "DOC019", "High Cholesterol", "01-09-2025"));
        treatments.add(new Treatment("TRE220", "20", "DOC020", "Allergy", "01-09-2025"));
        return treatments;
    }

    public static SetAndQueueInterface<PharmacyTransaction> initializeSampleTransactions() {
        SetAndQueueInterface<PharmacyTransaction> transactions = new SetQueueArray<>();
        transactions.add(new PharmacyTransaction("TXN001", "1", "MED007", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN002", "2", "MED006", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN003", "3", "MED003", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN004", "4", "MED013", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN005", "5", "MED023", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN006", "6", "MED001", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN007", "7", "MED008", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN008", "8", "MED019", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN009", "9", "MED005", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN010", "10", "MED030", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN011", "11", "MED014", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN012", "12", "MED012", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN013", "13", "MED003", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN014", "14", "MED025", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN015", "15", "MED034", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN016", "16", "MED001", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN017", "17", "MED008", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN018", "18", "MED019", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN019", "19", "MED035", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN020", "20", "MED030", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN021", "21", "MED007", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN022", "22", "MED006", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN023", "23", "MED003", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN024", "24", "MED013", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN025", "25", "MED023", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN026", "26", "MED001", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN027", "27", "MED008", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN028", "28", "MED019", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN029", "29", "MED005", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN030", "30", "MED030", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN031", "31", "MED014", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN032", "32", "MED012", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN033", "33", "MED003", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN034", "34", "MED025", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN035", "35", "MED034", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN036", "36", "MED001", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN037", "37", "MED008", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN038", "38", "MED019", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN039", "39", "MED035", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN040", "40", "MED030", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN041", "1", "MED007", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN042", "2", "MED006", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN043", "3", "MED003", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN044", "4", "MED013", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN045", "5", "MED023", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN046", "6", "MED001", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN047", "7", "MED008", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN048", "8", "MED019", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN049", "9", "MED005", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN050", "10", "MED030", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN051", "11", "MED014", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN052", "12", "MED012", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN053", "13", "MED003", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN054", "14", "MED025", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN055", "15", "MED034", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN056", "16", "MED001", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN057", "17", "MED008", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN058", "18", "MED019", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN059", "19", "MED035", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN060", "20", "MED030", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN061", "6", "MED007", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN062", "9", "MED001", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN063", "14", "MED019", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN064", "16", "MED010", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN065", "7", "MED011", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN066", "7", "MED012", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN067", "8", "MED020", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN068", "9", "MED018", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN069", "5", "MED015", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN070", "5", "MED016", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN071", "5", "MED017", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN072", "5", "MED011", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN073", "6", "MED002", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN074", "6", "MED040", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN075", "6", "MED021", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN076", "6", "MED022", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN077", "2", "MED032", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN078", "7", "MED006", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN079", "11", "MED009", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN080", "18", "MED007", 1, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN081", "1", "MED007", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN082", "4", "MED013", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN083", "23", "MED030", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN084", "1", "MED014", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN085", "5", "MED034", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN086", "24", "MED006", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN087", "26", "MED013", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN088", "5", "MED034", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN089", "10", "MED008", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN090", "10", "MED018", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN091", "10", "MED019", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN092", "10", "MED001", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN093", "11", "MED040", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN094", "11", "MED014", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN095", "11", "MED002", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN096", "11", "MED003", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN097", "3", "MED006", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN098", "8", "MED019", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN099", "15", "MED033", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN100", "20", "MED007", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN101", "1", "MED039", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN102", "3", "MED010", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN103", "32", "MED011", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN104", "1", "MED012", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN105", "12", "MED020", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN106", "12", "MED018", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN107", "12", "MED035", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN108", "12", "MED032", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN109", "5", "MED036", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN110", "13", "MED019", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN111", "14", "MED029", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN112", "5", "MED011", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN113", "12", "MED031", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN114", "15", "MED022", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN115", "16", "MED003", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN116", "12", "MED006", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN117", "2", "MED009", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN118", "7", "MED028", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN119", "11", "MED008", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN120", "18", "MED009", 1, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN121", "1", "MED010", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN122", "1", "MED011", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN123", "1", "MED012", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN124", "1", "MED013", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN125", "5", "MED018", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN126", "5", "MED034", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN127", "5", "MED031", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN128", "5", "MED007", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN129", "17", "MED019", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN130", "18", "MED002", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN131", "19", "MED037", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN132", "20", "MED001", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN133", "21", "MED022", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN134", "22", "MED003", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN135", "23", "MED006", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN136", "24", "MED009", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN137", "11", "MED028", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN138", "8", "MED038", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN139", "15", "MED039", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN140", "20", "MED010", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN141", "1", "MED011", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN142", "1", "MED012", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN143", "1", "MED013", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN144", "1", "MED014", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN145", "25", "MED015", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN146", "26", "MED016", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN147", "27", "MED017", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN148", "28", "MED018", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN149", "29", "MED019", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN150", "30", "MED020", 1, "05-09-2025"));
        
        transactions.add(new PharmacyTransaction("TXN161", "1", "MED012", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN162", "2", "MED013", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN163", "3", "MED018", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN164", "4", "MED023", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN165", "5", "MED016", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN166", "6", "MED017", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN167", "7", "MED019", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN168", "8", "MED040", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN169", "9", "MED001", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN170", "10", "MED010", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN171", "11", "MED022", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN172", "2", "MED032", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN173", "13", "MED006", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN174", "14", "MED019", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN175", "15", "MED028", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN176", "16", "MED001", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN177", "17", "MED039", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN178", "18", "MED010", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN179", "19", "MED011", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN180", "20", "MED012", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN181", "21", "MED013", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN182", "22", "MED014", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN183", "23", "MED015", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN184", "24", "MED016", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN185", "25", "MED017", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN186", "26", "MED018", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN187", "27", "MED019", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN188", "28", "MED040", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN189", "29", "MED001", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN190", "30", "MED010", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN191", "31", "MED011", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN192", "32", "MED012", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN193", "33", "MED013", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN194", "34", "MED014", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN195", "35", "MED015", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN196", "36", "MED016", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN197", "37", "MED017", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN198", "38", "MED018", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN199", "39", "MED019", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN200", "40", "MED040", 1, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN201", "1", "MED018", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN202", "2", "MED023", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN203", "3", "MED001", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN204", "4", "MED022", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN205", "5", "MED019", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN206", "6", "MED002", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN207", "7", "MED040", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN208", "8", "MED010", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN209", "9", "MED031", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN210", "10", "MED003", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN211", "11", "MED032", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN212", "12", "MED009", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN213", "13", "MED028", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN214", "14", "MED008", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN215", "15", "MED019", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN216", "16", "MED010", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN217", "17", "MED011", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN218", "18", "MED012", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN219", "19", "MED013", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN220", "20", "MED014", 1, "07-09-2025"));

        return transactions;
    }

    public static SetAndQueueInterface<Prescription> initializeSamplePrescriptions() {
        SetAndQueueInterface<Prescription> prescriptions = new SetQueueArray<>();
        SetAndQueueInterface<PrescribedMedicine> pmSet001 = new SetQueueArray<>();
        pmSet001.add(new PrescribedMedicine("PM001", "PRE001", "MED007", "Amlodipine", 1, "5mg daily", "Take in morning", 32.80, 32.80, false));
        prescriptions.add(new Prescription("PRE001", "CON001", "1", "DOC001", "Hypertension", pmSet001, "02-09-2025", "active", 32.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet002 = new SetQueueArray<>();
        pmSet002.add(new PrescribedMedicine("PM002", "PRE002", "MED006", "Metformin", 1, "500mg BID", "With meals", 22.40, 22.40, false));
        prescriptions.add(new Prescription("PRE002", "CON002", "2", "DOC002", "Diabetes", pmSet002, "02-09-2025", "active", 22.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet003 = new SetQueueArray<>();
        pmSet003.add(new PrescribedMedicine("PM003", "PRE003", "MED003", "Ibuprofen", 1, "200mg PRN", "After food", 12.90, 12.90, false));
        prescriptions.add(new Prescription("PRE003", "CON003", "3", "DOC003", "Migraine", pmSet003, "02-09-2025", "active", 12.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet004 = new SetQueueArray<>();
        pmSet004.add(new PrescribedMedicine("PM004", "PRE004", "MED013", "Loratadine", 1, "10mg daily", "At night", 16.80, 16.80, false));
        prescriptions.add(new Prescription("PRE004", "CON004", "4", "DOC004", "Allergy", pmSet004, "02-09-2025", "active", 16.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet005 = new SetQueueArray<>();
        pmSet005.add(new PrescribedMedicine("PM005", "PRE005", "MED023", "Diclofenac", 1, "50mg BID", "With water", 14.70, 14.70, false));
        prescriptions.add(new Prescription("PRE005", "CON005", "5", "DOC005", "Back Pain", pmSet005, "02-09-2025", "active", 14.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet006 = new SetQueueArray<>();
        pmSet006.add(new PrescribedMedicine("PM006", "PRE006", "MED001", "Paracetamol", 1, "500mg QID", "If fever", 8.50, 8.50, false));
        prescriptions.add(new Prescription("PRE006", "CON006", "6", "DOC006", "Flu", pmSet006, "02-09-2025", "active", 8.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet007 = new SetQueueArray<>();
        pmSet007.add(new PrescribedMedicine("PM007", "PRE007", "MED008", "Salbutamol", 1, "2 puffs PRN", "Inhaler", 28.90, 28.90, false));
        prescriptions.add(new Prescription("PRE007", "CON007", "7", "DOC007", "Asthma", pmSet007, "02-09-2025", "active", 28.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet008 = new SetQueueArray<>();
        pmSet008.add(new PrescribedMedicine("PM008", "PRE008", "MED019", "Duloxetine", 1, "30mg daily", "Morning", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE008", "CON008", "8", "DOC008", "Depression", pmSet008, "02-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet009 = new SetQueueArray<>();
        pmSet009.add(new PrescribedMedicine("PM009", "PRE009", "MED005", "Cetirizine", 1, "10mg daily", "Night", 18.20, 18.20, false));
        prescriptions.add(new Prescription("PRE009", "CON009", "9", "DOC009", "Thyroid Disorder", pmSet009, "02-09-2025", "active", 18.20, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet010 = new SetQueueArray<>();
        pmSet010.add(new PrescribedMedicine("PM010", "PRE010", "MED030", "Rosuvastatin", 1, "10mg nightly", "Avoid grapefruit", 62.70, 62.70, false));
        prescriptions.add(new Prescription("PRE010", "CON010", "10", "DOC010", "High Cholesterol", pmSet010, "02-09-2025", "active", 62.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet011 = new SetQueueArray<>();
        pmSet011.add(new PrescribedMedicine("PM011", "PRE011", "MED014", "Lisinopril", 1, "10mg daily", "Monitor BP", 28.40, 28.40, false));
        prescriptions.add(new Prescription("PRE011", "CON011", "11", "DOC011", "Hypertension", pmSet011, "02-09-2025", "active", 28.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet012 = new SetQueueArray<>();
        pmSet012.add(new PrescribedMedicine("PM012", "PRE012", "MED012", "Insulin", 1, "Sliding scale", "Refrigerate", 120.00, 120.00, false));
        prescriptions.add(new Prescription("PRE012", "CON012", "12", "DOC012", "Diabetes", pmSet012, "02-09-2025", "active", 120.00, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet013 = new SetQueueArray<>();
        pmSet013.add(new PrescribedMedicine("PM013", "PRE013", "MED003", "Ibuprofen", 1, "400mg PRN", "After meals", 12.90, 12.90, false));
        prescriptions.add(new Prescription("PRE013", "CON013", "13", "DOC013", "Migraine", pmSet013, "02-09-2025", "active", 12.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet014 = new SetQueueArray<>();
        pmSet014.add(new PrescribedMedicine("PM014", "PRE014", "MED025", "Fexofenadine", 1, "120mg daily", "Avoid alcohol", 21.50, 21.50, false));
        prescriptions.add(new Prescription("PRE014", "CON014", "14", "DOC014", "Allergy", pmSet014, "02-09-2025", "active", 21.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet015 = new SetQueueArray<>();
        pmSet015.add(new PrescribedMedicine("PM015", "PRE015", "MED034", "Tramadol", 1, "50mg PRN", "May cause drowsiness", 28.70, 28.70, false));
        prescriptions.add(new Prescription("PRE015", "CON015", "15", "DOC015", "Back Pain", pmSet015, "02-09-2025", "active", 28.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet016 = new SetQueueArray<>();
        pmSet016.add(new PrescribedMedicine("PM016", "PRE016", "MED001", "Paracetamol", 1, "500mg QID", "Hydrate well", 8.50, 8.50, false));
        prescriptions.add(new Prescription("PRE016", "CON016", "16", "DOC016", "Flu", pmSet016, "02-09-2025", "active", 8.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet017 = new SetQueueArray<>();
        pmSet017.add(new PrescribedMedicine("PM017", "PRE017", "MED008", "Salbutamol", 1, "2 puffs PRN", "Inhaler", 28.90, 28.90, false));
        prescriptions.add(new Prescription("PRE017", "CON017", "17", "DOC017", "Asthma", pmSet017, "02-09-2025", "active", 28.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet018 = new SetQueueArray<>();
        pmSet018.add(new PrescribedMedicine("PM018", "PRE018", "MED019", "Duloxetine", 1, "30mg daily", "Morning", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE018", "CON018", "18", "DOC018", "Depression", pmSet018, "02-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet019 = new SetQueueArray<>();
        pmSet019.add(new PrescribedMedicine("PM019", "PRE019", "MED035", "Warfarin", 1, "2mg daily", "Check INR", 12.30, 12.30, false));
        prescriptions.add(new Prescription("PRE019", "CON019", "19", "DOC019", "Thyroid Disorder", pmSet019, "02-09-2025", "active", 12.30, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet020 = new SetQueueArray<>();
        pmSet020.add(new PrescribedMedicine("PM020", "PRE020", "MED030", "Rosuvastatin", 1, "10mg nightly", "Avoid grapefruit", 62.70, 62.70, false));
        prescriptions.add(new Prescription("PRE020", "CON020", "20", "DOC020", "High Cholesterol", pmSet020, "02-09-2025", "active", 62.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet021 = new SetQueueArray<>();
        pmSet021.add(new PrescribedMedicine("PM021", "PRE021", "MED007", "Amlodipine", 1, "5mg daily", "Take in morning", 32.80, 32.80, false));
        prescriptions.add(new Prescription("PRE021", "CON021", "21", "DOC001", "Hypertension", pmSet021, "02-09-2025", "active", 32.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet022 = new SetQueueArray<>();
        pmSet022.add(new PrescribedMedicine("PM022", "PRE022", "MED006", "Metformin", 1, "500mg BID", "With meals", 22.40, 22.40, false));
        prescriptions.add(new Prescription("PRE022", "CON022", "22", "DOC002", "Diabetes", pmSet022, "02-09-2025", "active", 22.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet023 = new SetQueueArray<>();
        pmSet023.add(new PrescribedMedicine("PM023", "PRE023", "MED003", "Ibuprofen", 1, "200mg PRN", "After food", 12.90, 12.90, false));
        prescriptions.add(new Prescription("PRE023", "CON023", "23", "DOC003", "Migraine", pmSet023, "02-09-2025", "active", 12.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet024 = new SetQueueArray<>();
        pmSet024.add(new PrescribedMedicine("PM024", "PRE024", "MED013", "Loratadine", 1, "10mg daily", "At night", 16.80, 16.80, false));
        prescriptions.add(new Prescription("PRE024", "CON024", "24", "DOC004", "Allergy", pmSet024, "02-09-2025", "active", 16.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet025 = new SetQueueArray<>();
        pmSet025.add(new PrescribedMedicine("PM025", "PRE025", "MED023", "Diclofenac", 1, "50mg BID", "With water", 14.70, 14.70, false));
        prescriptions.add(new Prescription("PRE025", "CON025", "25", "DOC005", "Back Pain", pmSet025, "02-09-2025", "active", 14.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet026 = new SetQueueArray<>();
        pmSet026.add(new PrescribedMedicine("PM026", "PRE026", "MED001", "Paracetamol", 1, "500mg QID", "If fever", 8.50, 8.50, false));
        prescriptions.add(new Prescription("PRE026", "CON026", "26", "DOC006", "Flu", pmSet026, "02-09-2025", "active", 8.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet027 = new SetQueueArray<>();
        pmSet027.add(new PrescribedMedicine("PM027", "PRE027", "MED008", "Salbutamol", 1, "2 puffs PRN", "Inhaler", 28.90, 28.90, false));
        prescriptions.add(new Prescription("PRE027", "CON027", "27", "DOC007", "Asthma", pmSet027, "02-09-2025", "active", 28.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet028 = new SetQueueArray<>();
        pmSet028.add(new PrescribedMedicine("PM028", "PRE028", "MED019", "Duloxetine", 1, "30mg daily", "Morning", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE028", "CON028", "28", "DOC008", "Depression", pmSet028, "02-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet029 = new SetQueueArray<>();
        pmSet029.add(new PrescribedMedicine("PM029", "PRE029", "MED005", "Cetirizine", 1, "10mg daily", "Night", 18.20, 18.20, false));
        prescriptions.add(new Prescription("PRE029", "CON029", "29", "DOC009", "Thyroid Disorder", pmSet029, "02-09-2025", "active", 18.20, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet030 = new SetQueueArray<>();
        pmSet030.add(new PrescribedMedicine("PM030", "PRE030", "MED030", "Rosuvastatin", 1, "10mg nightly", "Avoid grapefruit", 62.70, 62.70, false));
        prescriptions.add(new Prescription("PRE030", "CON030", "30", "DOC010", "High Cholesterol", pmSet030, "02-09-2025", "active", 62.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet031 = new SetQueueArray<>();
        pmSet031.add(new PrescribedMedicine("PM031", "PRE031", "MED014", "Lisinopril", 1, "10mg daily", "Monitor BP", 28.40, 28.40, false));
        prescriptions.add(new Prescription("PRE031", "CON031", "31", "DOC011", "Hypertension", pmSet031, "02-09-2025", "active", 28.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet032 = new SetQueueArray<>();
        pmSet032.add(new PrescribedMedicine("PM032", "PRE032", "MED012", "Insulin", 1, "Sliding scale", "Refrigerate", 120.00, 120.00, false));
        prescriptions.add(new Prescription("PRE032", "CON032", "32", "DOC012", "Diabetes", pmSet032, "02-09-2025", "active", 120.00, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet033 = new SetQueueArray<>();
        pmSet033.add(new PrescribedMedicine("PM033", "PRE033", "MED003", "Ibuprofen", 1, "400mg PRN", "After meals", 12.90, 12.90, false));
        prescriptions.add(new Prescription("PRE033", "CON033", "33", "DOC013", "Migraine", pmSet033, "02-09-2025", "active", 12.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet034 = new SetQueueArray<>();
        pmSet034.add(new PrescribedMedicine("PM034", "PRE034", "MED025", "Fexofenadine", 1, "120mg daily", "Avoid alcohol", 21.50, 21.50, false));
        prescriptions.add(new Prescription("PRE034", "CON034", "34", "DOC014", "Allergy", pmSet034, "02-09-2025", "active", 21.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet035 = new SetQueueArray<>();
        pmSet035.add(new PrescribedMedicine("PM035", "PRE035", "MED034", "Tramadol", 1, "50mg PRN", "May cause drowsiness", 28.70, 28.70, false));
        prescriptions.add(new Prescription("PRE035", "CON035", "35", "DOC015", "Back Pain", pmSet035, "02-09-2025", "active", 28.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet036 = new SetQueueArray<>();
        pmSet036.add(new PrescribedMedicine("PM036", "PRE036", "MED001", "Paracetamol", 1, "500mg QID", "Hydrate well", 8.50, 8.50, false));
        prescriptions.add(new Prescription("PRE036", "CON036", "36", "DOC016", "Flu", pmSet036, "02-09-2025", "active", 8.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet037 = new SetQueueArray<>();
        pmSet037.add(new PrescribedMedicine("PM037", "PRE037", "MED008", "Salbutamol", 1, "2 puffs PRN", "Inhaler", 28.90, 28.90, false));
        prescriptions.add(new Prescription("PRE037", "CON037", "37", "DOC017", "Asthma", pmSet037, "02-09-2025", "active", 28.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet038 = new SetQueueArray<>();
        pmSet038.add(new PrescribedMedicine("PM038", "PRE038", "MED019", "Duloxetine", 1, "30mg daily", "Morning", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE038", "CON038", "38", "DOC018", "Depression", pmSet038, "02-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet039 = new SetQueueArray<>();
        pmSet039.add(new PrescribedMedicine("PM039", "PRE039", "MED035", "Warfarin", 1, "2mg daily", "Check INR", 12.30, 12.30, false));
        prescriptions.add(new Prescription("PRE039", "CON039", "39", "DOC019", "Thyroid Disorder", pmSet039, "02-09-2025", "active", 12.30, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet040 = new SetQueueArray<>();
        pmSet040.add(new PrescribedMedicine("PM040", "PRE040", "MED030", "Rosuvastatin", 1, "10mg nightly", "Avoid grapefruit", 62.70, 62.70, false));
        prescriptions.add(new Prescription("PRE040", "CON040", "40", "DOC020", "High Cholesterol", pmSet040, "02-09-2025", "active", 62.70, false));
        prescriptions.add(new Prescription("PRE021", "CON021", "21", "DOC001", "Hypertension", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE022", "CON022", "22", "DOC002", "Diabetes", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE023", "CON023", "23", "DOC003", "Migraine", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE024", "CON024", "24", "DOC004", "Allergy", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE025", "CON025", "25", "DOC005", "Back Pain", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE026", "CON026", "26", "DOC006", "Flu", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE027", "CON027", "27", "DOC007", "Asthma", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE028", "CON028", "28", "DOC008", "Depression", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE029", "CON029", "29", "DOC009", "Thyroid Disorder", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE030", "CON030", "30", "DOC010", "High Cholesterol", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE031", "CON031", "31", "DOC011", "Hypertension", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE032", "CON032", "32", "DOC012", "Diabetes", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE033", "CON033", "33", "DOC013", "Migraine", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE034", "CON034", "34", "DOC014", "Allergy", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE035", "CON035", "35", "DOC015", "Back Pain", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE036", "CON036", "36", "DOC016", "Flu", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE037", "CON037", "37", "DOC017", "Asthma", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE038", "CON038", "38", "DOC018", "Depression", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE039", "CON039", "39", "DOC019", "Thyroid Disorder", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE040", "CON040", "40", "DOC020", "High Cholesterol", new SetQueueArray<>(), "02-09-2025", "active", 0.0, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet041 = new SetQueueArray<>();
        pmSet041.add(new PrescribedMedicine("PM041", "PRE041", "MED007", "Amlodipine", 1, "5mg daily", "Take in morning", 32.80, 32.80, false));
        prescriptions.add(new Prescription("PRE041", "CON041", "1", "DOC001", "Hypertension", pmSet041, "03-09-2025", "active", 32.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet042 = new SetQueueArray<>();
        pmSet042.add(new PrescribedMedicine("PM042", "PRE042", "MED006", "Metformin", 1, "500mg BID", "With meals", 22.40, 22.40, false));
        prescriptions.add(new Prescription("PRE042", "CON042", "2", "DOC002", "Diabetes", pmSet042, "03-09-2025", "active", 22.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet043 = new SetQueueArray<>();
        pmSet043.add(new PrescribedMedicine("PM043", "PRE043", "MED003", "Ibuprofen", 1, "200mg PRN", "After food", 12.90, 12.90, false));
        prescriptions.add(new Prescription("PRE043", "CON043", "3", "DOC003", "Migraine", pmSet043, "03-09-2025", "active", 12.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet044 = new SetQueueArray<>();
        pmSet044.add(new PrescribedMedicine("PM044", "PRE044", "MED013", "Loratadine", 1, "10mg daily", "At night", 16.80, 16.80, false));
        prescriptions.add(new Prescription("PRE044", "CON044", "4", "DOC004", "Allergy", pmSet044, "03-09-2025", "active", 16.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet045 = new SetQueueArray<>();
        pmSet045.add(new PrescribedMedicine("PM045", "PRE045", "MED023", "Diclofenac", 1, "50mg BID", "With water", 14.70, 14.70, false));
        prescriptions.add(new Prescription("PRE045", "CON045", "5", "DOC005", "Back Pain", pmSet045, "03-09-2025", "active", 14.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet046 = new SetQueueArray<>();
        pmSet046.add(new PrescribedMedicine("PM046", "PRE046", "MED001", "Paracetamol", 1, "500mg QID", "If fever", 8.50, 8.50, false));
        prescriptions.add(new Prescription("PRE046", "CON046", "6", "DOC006", "Flu", pmSet046, "03-09-2025", "active", 8.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet047 = new SetQueueArray<>();
        pmSet047.add(new PrescribedMedicine("PM047", "PRE047", "MED008", "Salbutamol", 1, "2 puffs PRN", "Inhaler", 28.90, 28.90, false));
        prescriptions.add(new Prescription("PRE047", "CON047", "7", "DOC007", "Asthma", pmSet047, "03-09-2025", "active", 28.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet048 = new SetQueueArray<>();
        pmSet048.add(new PrescribedMedicine("PM048", "PRE048", "MED019", "Duloxetine", 1, "30mg daily", "Morning", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE048", "CON048", "8", "DOC008", "Depression", pmSet048, "03-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet049 = new SetQueueArray<>();
        pmSet049.add(new PrescribedMedicine("PM049", "PRE049", "MED005", "Cetirizine", 1, "10mg daily", "Night", 18.20, 18.20, false));
        prescriptions.add(new Prescription("PRE049", "CON049", "9", "DOC009", "Thyroid Disorder", pmSet049, "03-09-2025", "active", 18.20, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet050 = new SetQueueArray<>();
        pmSet050.add(new PrescribedMedicine("PM050", "PRE050", "MED030", "Rosuvastatin", 1, "10mg nightly", "Avoid grapefruit", 62.70, 62.70, false));
        prescriptions.add(new Prescription("PRE050", "CON050", "10", "DOC010", "High Cholesterol", pmSet050, "03-09-2025", "active", 62.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet051 = new SetQueueArray<>();
        pmSet051.add(new PrescribedMedicine("PM051", "PRE051", "MED014", "Lisinopril", 1, "10mg daily", "Monitor BP", 28.40, 28.40, false));
        prescriptions.add(new Prescription("PRE051", "CON051", "11", "DOC011", "Hypertension", pmSet051, "03-09-2025", "active", 28.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet052 = new SetQueueArray<>();
        pmSet052.add(new PrescribedMedicine("PM052", "PRE052", "MED012", "Insulin", 1, "Sliding scale", "Refrigerate", 120.00, 120.00, false));
        prescriptions.add(new Prescription("PRE052", "CON052", "12", "DOC012", "Diabetes", pmSet052, "03-09-2025", "active", 120.00, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet053 = new SetQueueArray<>();
        pmSet053.add(new PrescribedMedicine("PM053", "PRE053", "MED003", "Ibuprofen", 1, "400mg PRN", "After meals", 12.90, 12.90, false));
        prescriptions.add(new Prescription("PRE053", "CON053", "13", "DOC013", "Migraine", pmSet053, "03-09-2025", "active", 12.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet054 = new SetQueueArray<>();
        pmSet054.add(new PrescribedMedicine("PM054", "PRE054", "MED025", "Fexofenadine", 1, "120mg daily", "Avoid alcohol", 21.50, 21.50, false));
        prescriptions.add(new Prescription("PRE054", "CON054", "14", "DOC014", "Allergy", pmSet054, "03-09-2025", "active", 21.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet055 = new SetQueueArray<>();
        pmSet055.add(new PrescribedMedicine("PM055", "PRE055", "MED034", "Tramadol", 1, "50mg PRN", "May cause drowsiness", 28.70, 28.70, false));
        prescriptions.add(new Prescription("PRE055", "CON055", "15", "DOC015", "Back Pain", pmSet055, "03-09-2025", "active", 28.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet056 = new SetQueueArray<>();
        pmSet056.add(new PrescribedMedicine("PM056", "PRE056", "MED001", "Paracetamol", 1, "500mg QID", "Hydrate well", 8.50, 8.50, false));
        prescriptions.add(new Prescription("PRE056", "CON056", "16", "DOC016", "Flu", pmSet056, "03-09-2025", "active", 8.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet057 = new SetQueueArray<>();
        pmSet057.add(new PrescribedMedicine("PM057", "PRE057", "MED008", "Salbutamol", 1, "2 puffs PRN", "Inhaler", 28.90, 28.90, false));
        prescriptions.add(new Prescription("PRE057", "CON057", "17", "DOC017", "Asthma", pmSet057, "03-09-2025", "active", 28.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet058 = new SetQueueArray<>();
        pmSet058.add(new PrescribedMedicine("PM058", "PRE058", "MED019", "Duloxetine", 1, "30mg daily", "Morning", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE058", "CON058", "18", "DOC018", "Depression", pmSet058, "03-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet059 = new SetQueueArray<>();
        pmSet059.add(new PrescribedMedicine("PM059", "PRE059", "MED035", "Warfarin", 1, "2mg daily", "Check INR", 12.30, 12.30, false));
        prescriptions.add(new Prescription("PRE059", "CON059", "19", "DOC019", "Thyroid Disorder", pmSet059, "03-09-2025", "active", 12.30, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet060 = new SetQueueArray<>();
        pmSet060.add(new PrescribedMedicine("PM060", "PRE060", "MED030", "Rosuvastatin", 1, "10mg nightly", "Avoid grapefruit", 62.70, 62.70, false));
        prescriptions.add(new Prescription("PRE060", "CON060", "20", "DOC020", "High Cholesterol", pmSet060, "03-09-2025", "active", 62.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet061b = new SetQueueArray<>();
        pmSet061b.add(new PrescribedMedicine("PM061B", "PRE061", "MED007", "Amlodipine", 1, "5mg daily", "Take in morning", 32.80, 32.80, false));
        prescriptions.add(new Prescription("PRE061", "CON061", "21", "DOC001", "Hypertension", pmSet061b, "03-09-2025", "active", 32.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet062b = new SetQueueArray<>();
        pmSet062b.add(new PrescribedMedicine("PM062B", "PRE062", "MED006", "Metformin", 1, "500mg BID", "With meals", 22.40, 22.40, false));
        prescriptions.add(new Prescription("PRE062", "CON062", "22", "DOC002", "Diabetes", pmSet062b, "03-09-2025", "active", 22.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet063b = new SetQueueArray<>();
        pmSet063b.add(new PrescribedMedicine("PM063B", "PRE063", "MED003", "Ibuprofen", 1, "200mg PRN", "After food", 12.90, 12.90, false));
        prescriptions.add(new Prescription("PRE063", "CON063", "23", "DOC003", "Migraine", pmSet063b, "03-09-2025", "active", 12.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet064b = new SetQueueArray<>();
        pmSet064b.add(new PrescribedMedicine("PM064B", "PRE064", "MED013", "Loratadine", 1, "10mg daily", "At night", 16.80, 16.80, false));
        prescriptions.add(new Prescription("PRE064", "CON064", "24", "DOC004", "Allergy", pmSet064b, "03-09-2025", "active", 16.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet065b = new SetQueueArray<>();
        pmSet065b.add(new PrescribedMedicine("PM065B", "PRE065", "MED023", "Diclofenac", 1, "50mg BID", "With water", 14.70, 14.70, false));
        prescriptions.add(new Prescription("PRE065", "CON065", "25", "DOC005", "Back Pain", pmSet065b, "03-09-2025", "active", 14.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet066b = new SetQueueArray<>();
        pmSet066b.add(new PrescribedMedicine("PM066B", "PRE066", "MED001", "Paracetamol", 1, "500mg QID", "If fever", 8.50, 8.50, false));
        prescriptions.add(new Prescription("PRE066", "CON066", "26", "DOC006", "Flu", pmSet066b, "03-09-2025", "active", 8.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet067b = new SetQueueArray<>();
        pmSet067b.add(new PrescribedMedicine("PM067B", "PRE067", "MED008", "Salbutamol", 1, "2 puffs PRN", "Inhaler", 28.90, 28.90, false));
        prescriptions.add(new Prescription("PRE067", "CON067", "27", "DOC007", "Asthma", pmSet067b, "03-09-2025", "active", 28.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet068b = new SetQueueArray<>();
        pmSet068b.add(new PrescribedMedicine("PM068B", "PRE068", "MED019", "Duloxetine", 1, "30mg daily", "Morning", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE068", "CON068", "28", "DOC008", "Depression", pmSet068b, "03-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet069b = new SetQueueArray<>();
        pmSet069b.add(new PrescribedMedicine("PM069B", "PRE069", "MED005", "Cetirizine", 1, "10mg daily", "Night", 18.20, 18.20, false));
        prescriptions.add(new Prescription("PRE069", "CON069", "29", "DOC009", "Thyroid Disorder", pmSet069b, "03-09-2025", "active", 18.20, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet070b = new SetQueueArray<>();
        pmSet070b.add(new PrescribedMedicine("PM070B", "PRE070", "MED030", "Rosuvastatin", 1, "10mg nightly", "Avoid grapefruit", 62.70, 62.70, false));
        prescriptions.add(new Prescription("PRE070", "CON070", "30", "DOC010", "High Cholesterol", pmSet070b, "03-09-2025", "active", 62.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet071b = new SetQueueArray<>();
        pmSet071b.add(new PrescribedMedicine("PM071B", "PRE071", "MED014", "Lisinopril", 1, "10mg daily", "Monitor BP", 28.40, 28.40, false));
        prescriptions.add(new Prescription("PRE071", "CON071", "31", "DOC011", "Hypertension", pmSet071b, "03-09-2025", "active", 28.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet072b = new SetQueueArray<>();
        pmSet072b.add(new PrescribedMedicine("PM072B", "PRE072", "MED012", "Insulin", 1, "Sliding scale", "Refrigerate", 120.00, 120.00, false));
        prescriptions.add(new Prescription("PRE072", "CON072", "32", "DOC012", "Diabetes", pmSet072b, "03-09-2025", "active", 120.00, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet073b = new SetQueueArray<>();
        pmSet073b.add(new PrescribedMedicine("PM073B", "PRE073", "MED003", "Ibuprofen", 1, "400mg PRN", "After meals", 12.90, 12.90, false));
        prescriptions.add(new Prescription("PRE073", "CON073", "33", "DOC013", "Migraine", pmSet073b, "03-09-2025", "active", 12.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet074b = new SetQueueArray<>();
        pmSet074b.add(new PrescribedMedicine("PM074B", "PRE074", "MED025", "Fexofenadine", 1, "120mg daily", "Avoid alcohol", 21.50, 21.50, false));
        prescriptions.add(new Prescription("PRE074", "CON074", "34", "DOC014", "Allergy", pmSet074b, "03-09-2025", "active", 21.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet075b = new SetQueueArray<>();
        pmSet075b.add(new PrescribedMedicine("PM075B", "PRE075", "MED034", "Tramadol", 1, "50mg PRN", "May cause drowsiness", 28.70, 28.70, false));
        prescriptions.add(new Prescription("PRE075", "CON075", "35", "DOC015", "Back Pain", pmSet075b, "03-09-2025", "active", 28.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet076b = new SetQueueArray<>();
        pmSet076b.add(new PrescribedMedicine("PM076B", "PRE076", "MED001", "Paracetamol", 1, "500mg QID", "Hydrate well", 8.50, 8.50, false));
        prescriptions.add(new Prescription("PRE076", "CON076", "36", "DOC016", "Flu", pmSet076b, "03-09-2025", "active", 8.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet077b = new SetQueueArray<>();
        pmSet077b.add(new PrescribedMedicine("PM077B", "PRE077", "MED008", "Salbutamol", 1, "2 puffs PRN", "Inhaler", 28.90, 28.90, false));
        prescriptions.add(new Prescription("PRE077", "CON077", "37", "DOC017", "Asthma", pmSet077b, "03-09-2025", "active", 28.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet078b = new SetQueueArray<>();
        pmSet078b.add(new PrescribedMedicine("PM078B", "PRE078", "MED019", "Duloxetine", 1, "30mg daily", "Morning", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE078", "CON078", "38", "DOC018", "Depression", pmSet078b, "03-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet079b = new SetQueueArray<>();
        pmSet079b.add(new PrescribedMedicine("PM079B", "PRE079", "MED035", "Warfarin", 1, "2mg daily", "Check INR", 12.30, 12.30, false));
        prescriptions.add(new Prescription("PRE079", "CON079", "39", "DOC019", "Thyroid Disorder", pmSet079b, "03-09-2025", "active", 12.30, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet080b = new SetQueueArray<>();
        pmSet080b.add(new PrescribedMedicine("PM080B", "PRE080", "MED030", "Rosuvastatin", 1, "10mg nightly", "Avoid grapefruit", 62.70, 62.70, false));
        prescriptions.add(new Prescription("PRE080", "CON080", "40", "DOC020", "High Cholesterol", pmSet080b, "03-09-2025", "active", 62.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet081 = new SetQueueArray<>();
        pmSet081.add(new PrescribedMedicine("PM081", "PRE081", "MED007", "Amlodipine", 1, "5mg daily", "", 32.80, 32.80, false));
        prescriptions.add(new Prescription("PRE081", "CON081", "1", "DOC001", "Hypertension", pmSet081, "04-09-2025", "active", 32.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet082 = new SetQueueArray<>();
        pmSet082.add(new PrescribedMedicine("PM082", "PRE082", "MED013", "Loratadine", 1, "10mg daily", "", 16.80, 16.80, false));
        prescriptions.add(new Prescription("PRE082", "CON082", "4", "DOC009", "Allergy", pmSet082, "04-09-2025", "active", 16.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet083 = new SetQueueArray<>();
        pmSet083.add(new PrescribedMedicine("PM083", "PRE083", "MED030", "Rosuvastatin", 1, "10mg nightly", "Avoid grapefruit", 62.70, 62.70, false));
        prescriptions.add(new Prescription("PRE083", "CON083", "23", "DOC010", "High Cholesterol", pmSet083, "04-09-2025", "active", 62.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet084 = new SetQueueArray<>();
        pmSet084.add(new PrescribedMedicine("PM084", "PRE084", "MED014", "Lisinopril", 1, "10mg daily", "Monitor BP", 28.40, 28.40, false));
        prescriptions.add(new Prescription("PRE084", "CON084", "1", "DOC011", "Hypertension", pmSet084, "04-09-2025", "active", 28.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet085 = new SetQueueArray<>();
        pmSet085.add(new PrescribedMedicine("PM085", "PRE085", "MED034", "Tramadol", 1, "50mg PRN", "May cause drowsiness", 28.70, 28.70, false));
        prescriptions.add(new Prescription("PRE085", "CON085", "5", "DOC012", "Back Pain", pmSet085, "04-09-2025", "active", 28.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet086 = new SetQueueArray<>();
        pmSet086.add(new PrescribedMedicine("PM086", "PRE086", "MED006", "Metformin", 1, "500mg BID", "With meals", 22.40, 22.40, false));
        prescriptions.add(new Prescription("PRE086", "CON086", "24", "DOC013", "Diabetes", pmSet086, "04-09-2025", "active", 22.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet087 = new SetQueueArray<>();
        pmSet087.add(new PrescribedMedicine("PM087", "PRE087", "MED013", "Loratadine", 1, "10mg daily", "", 16.80, 16.80, false));
        prescriptions.add(new Prescription("PRE087", "CON087", "26", "DOC014", "Allergy", pmSet087, "04-09-2025", "active", 16.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet088 = new SetQueueArray<>();
        pmSet088.add(new PrescribedMedicine("PM088", "PRE088", "MED034", "Tramadol", 1, "50mg PRN", "", 28.70, 28.70, false));
        prescriptions.add(new Prescription("PRE088", "CON088", "5", "DOC015", "Back Pain", pmSet088, "04-09-2025", "active", 28.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet089 = new SetQueueArray<>();
        pmSet089.add(new PrescribedMedicine("PM089", "PRE089", "MED008", "Salbutamol", 1, "2 puffs PRN", "", 28.90, 28.90, false));
        prescriptions.add(new Prescription("PRE089", "CON089", "10", "DOC016", "Asthma", pmSet089, "04-09-2025", "active", 28.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet090 = new SetQueueArray<>();
        pmSet090.add(new PrescribedMedicine("PM090", "PRE090", "MED018", "Pantoprazole", 1, "40mg daily", "", 31.20, 31.20, false));
        prescriptions.add(new Prescription("PRE090", "CON090", "10", "DOC017", "Thyroid Disorder", pmSet090, "04-09-2025", "active", 31.20, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet091 = new SetQueueArray<>();
        pmSet091.add(new PrescribedMedicine("PM091", "PRE091", "MED019", "Duloxetine", 1, "30mg daily", "", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE091", "CON091", "10", "DOC018", "Depression", pmSet091, "04-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet092 = new SetQueueArray<>();
        pmSet092.add(new PrescribedMedicine("PM092", "PRE092", "MED001", "Paracetamol", 1, "500mg QID", "", 8.50, 8.50, false));
        prescriptions.add(new Prescription("PRE092", "CON092", "10", "DOC019", "Flu", pmSet092, "04-09-2025", "active", 8.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet093 = new SetQueueArray<>();
        pmSet093.add(new PrescribedMedicine("PM093", "PRE093", "MED040", "Clonazepam", 1, "0.5mg nightly", "", 19.60, 19.60, false));
        prescriptions.add(new Prescription("PRE093", "CON093", "11", "DOC020", "Surgical Follow-up", pmSet093, "04-09-2025", "active", 19.60, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet094 = new SetQueueArray<>();
        pmSet094.add(new PrescribedMedicine("PM094", "PRE094", "MED014", "Lisinopril", 1, "10mg daily", "", 28.40, 28.40, false));
        prescriptions.add(new Prescription("PRE094", "CON094", "11", "DOC001", "Hypertension", pmSet094, "04-09-2025", "active", 28.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet095 = new SetQueueArray<>();
        pmSet095.add(new PrescribedMedicine("PM095", "PRE095", "MED002", "Amoxicillin", 1, "500mg TID", "Complete course", 15.80, 15.80, false));
        prescriptions.add(new Prescription("PRE095", "CON095", "11", "DOC002", "Pediatrics Review", pmSet095, "04-09-2025", "active", 15.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet096 = new SetQueueArray<>();
        pmSet096.add(new PrescribedMedicine("PM096", "PRE096", "MED003", "Ibuprofen", 1, "400mg PRN", "After meals", 12.90, 12.90, false));
        prescriptions.add(new Prescription("PRE096", "CON096", "11", "DOC003", "Migraine", pmSet096, "04-09-2025", "active", 12.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet097 = new SetQueueArray<>();
        pmSet097.add(new PrescribedMedicine("PM097", "PRE097", "MED006", "Metformin", 1, "500mg BID", "With meals", 22.40, 22.40, false));
        prescriptions.add(new Prescription("PRE097", "CON097", "3", "DOC005", "Diabetes", pmSet097, "04-09-2025", "active", 22.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet098 = new SetQueueArray<>();
        pmSet098.add(new PrescribedMedicine("PM098", "PRE098", "MED019", "Duloxetine", 1, "30mg daily", "Morning", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE098", "CON098", "8", "DOC006", "Depression", pmSet098, "04-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet099 = new SetQueueArray<>();
        pmSet099.add(new PrescribedMedicine("PM099", "PRE099", "MED033", "Escitalopram", 1, "10mg daily", "", 48.90, 48.90, false));
        prescriptions.add(new Prescription("PRE099", "CON099", "15", "DOC007", "Acne", pmSet099, "04-09-2025", "active", 48.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet100 = new SetQueueArray<>();
        pmSet100.add(new PrescribedMedicine("PM100", "PRE100", "MED007", "Amlodipine", 1, "5mg daily", "", 32.80, 32.80, false));
        prescriptions.add(new Prescription("PRE100", "CON100", "20", "DOC008", "Back Pain", pmSet100, "04-09-2025", "active", 32.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet101 = new SetQueueArray<>();
        pmSet101.add(new PrescribedMedicine("PM101", "PRE101", "MED039", "Gabapentin", 1, "300mg nightly", "", 31.40, 31.40, false));
        prescriptions.add(new Prescription("PRE101", "CON101", "1", "DOC009", "Oncology", pmSet101, "04-09-2025", "active", 31.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet102 = new SetQueueArray<>();
        pmSet102.add(new PrescribedMedicine("PM102", "PRE102", "MED010", "Atorvastatin", 1, "20mg nightly", "", 58.90, 58.90, false));
        prescriptions.add(new Prescription("PRE102", "CON102", "3", "DOC010", "Imaging Review", pmSet102, "04-09-2025", "active", 58.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet103 = new SetQueueArray<>();
        pmSet103.add(new PrescribedMedicine("PM103", "PRE103", "MED011", "Aspirin", 1, "75mg daily", "", 6.50, 6.50, false));
        prescriptions.add(new Prescription("PRE103", "CON103", "32", "DOC011", "OBGYN", pmSet103, "04-09-2025", "active", 6.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet104 = new SetQueueArray<>();
        pmSet104.add(new PrescribedMedicine("PM104", "PRE104", "MED012", "Insulin", 1, "Sliding scale", "", 120.00, 120.00, false));
        prescriptions.add(new Prescription("PRE104", "CON104", "1", "DOC012", "Urology", pmSet104, "04-09-2025", "active", 120.00, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet105 = new SetQueueArray<>();
        pmSet105.add(new PrescribedMedicine("PM105", "PRE105", "MED020", "Losartan", 1, "50mg daily", "", 29.60, 29.60, false));
        prescriptions.add(new Prescription("PRE105", "CON105", "12", "DOC013", "Cholesterol", pmSet105, "04-09-2025", "active", 29.60, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet106 = new SetQueueArray<>();
        pmSet106.add(new PrescribedMedicine("PM106", "PRE106", "MED018", "Pantoprazole", 1, "40mg daily", "", 31.20, 31.20, false));
        prescriptions.add(new Prescription("PRE106", "CON106", "12", "DOC014", "IBD", pmSet106, "04-09-2025", "active", 31.20, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet107 = new SetQueueArray<>();
        pmSet107.add(new PrescribedMedicine("PM107", "PRE107", "MED035", "Warfarin", 1, "2mg daily", "", 12.30, 12.30, false));
        prescriptions.add(new Prescription("PRE107", "CON107", "12", "DOC015", "Osteoporosis", pmSet107, "04-09-2025", "active", 12.30, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet108 = new SetQueueArray<>();
        pmSet108.add(new PrescribedMedicine("PM108", "PRE108", "MED032", "Metoprolol", 1, "50mg BID", "", 24.60, 24.60, false));
        prescriptions.add(new Prescription("PRE108", "CON108", "12", "DOC016", "COPD", pmSet108, "04-09-2025", "active", 24.60, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet109 = new SetQueueArray<>();
        pmSet109.add(new PrescribedMedicine("PM109", "PRE109", "MED036", "Levothyroxine", 1, "50mcg daily", "", 18.90, 18.90, false));
        prescriptions.add(new Prescription("PRE109", "CON109", "5", "DOC017", "CKD", pmSet109, "04-09-2025", "active", 18.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet110 = new SetQueueArray<>();
        pmSet110.add(new PrescribedMedicine("PM110", "PRE110", "MED019", "Duloxetine", 1, "30mg daily", "", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE110", "CON110", "13", "DOC018", "Hematology", pmSet110, "04-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet111 = new SetQueueArray<>();
        pmSet111.add(new PrescribedMedicine("PM111", "PRE111", "MED029", "Escitalopram", 1, "10mg daily", "", 48.90, 48.90, false));
        prescriptions.add(new Prescription("PRE111", "CON111", "14", "DOC019", "Hepatitis", pmSet111, "04-09-2025", "active", 48.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet112 = new SetQueueArray<>();
        pmSet112.add(new PrescribedMedicine("PM112", "PRE112", "MED011", "Aspirin", 1, "75mg daily", "", 6.50, 6.50, false));
        prescriptions.add(new Prescription("PRE112", "CON112", "5", "DOC020", "Post-op", pmSet112, "04-09-2025", "active", 6.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet113 = new SetQueueArray<>();
        pmSet113.add(new PrescribedMedicine("PM113", "PRE113", "MED031", "Naproxen", 1, "250mg BID", "", 11.40, 11.40, false));
        prescriptions.add(new Prescription("PRE113", "CON113", "12", "DOC001", "Arrhythmia", pmSet113, "04-09-2025", "active", 11.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet114 = new SetQueueArray<>();
        pmSet114.add(new PrescribedMedicine("PM114", "PRE114", "MED022", "Cephalexin", 1, "500mg QID", "", 18.90, 18.90, false));
        prescriptions.add(new Prescription("PRE114", "CON114", "15", "DOC002", "Pediatrics", pmSet114, "04-09-2025", "active", 18.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet115 = new SetQueueArray<>();
        pmSet115.add(new PrescribedMedicine("PM115", "PRE115", "MED003", "Ibuprofen", 1, "400mg PRN", "", 12.90, 12.90, false));
        prescriptions.add(new Prescription("PRE115", "CON115", "16", "DOC003", "Neurology", pmSet115, "04-09-2025", "active", 12.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet116 = new SetQueueArray<>();
        pmSet116.add(new PrescribedMedicine("PM116", "PRE116", "MED006", "Metformin", 1, "500mg BID", "", 22.40, 22.40, false));
        prescriptions.add(new Prescription("PRE116", "CON116", "12", "DOC005", "Endocrine", pmSet116, "04-09-2025", "active", 22.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet117 = new SetQueueArray<>();
        pmSet117.add(new PrescribedMedicine("PM117", "PRE117", "MED009", "Sertraline", 1, "50mg daily", "", 45.60, 45.60, false));
        prescriptions.add(new Prescription("PRE117", "CON117", "2", "DOC006", "Bipolar", pmSet117, "04-09-2025", "active", 45.60, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet118 = new SetQueueArray<>();
        pmSet118.add(new PrescribedMedicine("PM118", "PRE118", "MED028", "Budesonide", 1, "1 puff BID", "", 52.30, 52.30, false));
        prescriptions.add(new Prescription("PRE118", "CON118", "7", "DOC007", "Psoriasis", pmSet118, "04-09-2025", "active", 52.30, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet119 = new SetQueueArray<>();
        pmSet119.add(new PrescribedMedicine("PM119", "PRE119", "MED008", "Salbutamol", 1, "2 puffs PRN", "", 28.90, 28.90, false));
        prescriptions.add(new Prescription("PRE119", "CON119", "11", "DOC008", "Emergency", pmSet119, "04-09-2025", "active", 28.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet120 = new SetQueueArray<>();
        pmSet120.add(new PrescribedMedicine("PM120", "PRE120", "MED009", "Sertraline", 1, "50mg daily", "", 45.60, 45.60, false));
        prescriptions.add(new Prescription("PRE120", "CON120", "18", "DOC009", "Radiation", pmSet120, "04-09-2025", "active", 45.60, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet121 = new SetQueueArray<>();
        pmSet121.add(new PrescribedMedicine("PM121", "PRE121", "MED010", "Atorvastatin", 1, "20mg nightly", "", 58.90, 58.90, false));
        prescriptions.add(new Prescription("PRE121", "CON121", "1", "DOC010", "Hypertension", pmSet121, "05-09-2025", "active", 58.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet122 = new SetQueueArray<>();
        pmSet122.add(new PrescribedMedicine("PM122", "PRE122", "MED011", "Aspirin", 1, "75mg daily", "", 6.50, 6.50, false));
        prescriptions.add(new Prescription("PRE122", "CON122", "1", "DOC011", "Hypertension", pmSet122, "05-09-2025", "active", 6.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet123 = new SetQueueArray<>();
        pmSet123.add(new PrescribedMedicine("PM123", "PRE123", "MED012", "Insulin", 1, "Sliding scale", "", 120.00, 120.00, false));
        prescriptions.add(new Prescription("PRE123", "CON123", "1", "DOC012", "Hypertension", pmSet123, "05-09-2025", "active", 120.00, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet124 = new SetQueueArray<>();
        pmSet124.add(new PrescribedMedicine("PM124", "PRE124", "MED013", "Loratadine", 1, "10mg daily", "", 16.80, 16.80, false));
        prescriptions.add(new Prescription("PRE124", "CON124", "1", "DOC013", "Hypertension", pmSet124, "05-09-2025", "active", 16.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet125 = new SetQueueArray<>();
        pmSet125.add(new PrescribedMedicine("PM125", "PRE125", "MED018", "Pantoprazole", 1, "40mg daily", "", 31.20, 31.20, false));
        prescriptions.add(new Prescription("PRE125", "CON125", "5", "DOC014", "Hypertension", pmSet125, "05-09-2025", "active", 31.20, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet126 = new SetQueueArray<>();
        pmSet126.add(new PrescribedMedicine("PM126", "PRE126", "MED034", "Tramadol", 1, "50mg PRN", "", 28.70, 28.70, false));
        prescriptions.add(new Prescription("PRE126", "CON126", "5", "DOC015", "Hypertension", pmSet126, "05-09-2025", "active", 28.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet127 = new SetQueueArray<>();
        pmSet127.add(new PrescribedMedicine("PM127", "PRE127", "MED031", "Naproxen", 1, "250mg BID", "", 11.40, 11.40, false));
        prescriptions.add(new Prescription("PRE127", "CON127", "5", "DOC016", "Hypertension", pmSet127, "05-09-2025", "active", 11.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet128 = new SetQueueArray<>();
        pmSet128.add(new PrescribedMedicine("PM128", "PRE128", "MED007", "Amlodipine", 1, "5mg daily", "", 32.80, 32.80, false));
        prescriptions.add(new Prescription("PRE128", "CON128", "5", "DOC017", "Hypertension", pmSet128, "05-09-2025", "active", 32.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet129 = new SetQueueArray<>();
        pmSet129.add(new PrescribedMedicine("PM129", "PRE129", "MED019", "Duloxetine", 1, "30mg daily", "", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE129", "CON129", "17", "DOC018", "Hypertension", pmSet129, "05-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet130 = new SetQueueArray<>();
        pmSet130.add(new PrescribedMedicine("PM130", "PRE130", "MED002", "Amoxicillin", 1, "500mg TID", "Complete course", 15.80, 15.80, false));
        prescriptions.add(new Prescription("PRE130", "CON130", "18", "DOC019", "Hypertension", pmSet130, "05-09-2025", "active", 15.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet131 = new SetQueueArray<>();
        pmSet131.add(new PrescribedMedicine("PM131", "PRE131", "MED037", "Hydrochlorothiazide", 1, "25mg daily", "", 16.50, 16.50, false));
        prescriptions.add(new Prescription("PRE131", "CON131", "19", "DOC020", "Hypertension", pmSet131, "05-09-2025", "active", 16.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet132 = new SetQueueArray<>();
        pmSet132.add(new PrescribedMedicine("PM132", "PRE132", "MED001", "Paracetamol", 1, "500mg QID", "", 8.50, 8.50, false));
        prescriptions.add(new Prescription("PRE132", "CON132", "20", "DOC001", "Hypertension", pmSet132, "05-09-2025", "active", 8.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet133 = new SetQueueArray<>();
        pmSet133.add(new PrescribedMedicine("PM133", "PRE133", "MED022", "Cephalexin", 1, "500mg QID", "", 18.90, 18.90, false));
        prescriptions.add(new Prescription("PRE133", "CON133", "21", "DOC002", "Hypertension", pmSet133, "05-09-2025", "active", 18.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet134 = new SetQueueArray<>();
        pmSet134.add(new PrescribedMedicine("PM134", "PRE134", "MED003", "Ibuprofen", 1, "400mg PRN", "", 12.90, 12.90, false));
        prescriptions.add(new Prescription("PRE134", "CON134", "22", "DOC003", "Hypertension", pmSet134, "05-09-2025", "active", 12.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet135 = new SetQueueArray<>();
        pmSet135.add(new PrescribedMedicine("PM135", "PRE135", "MED006", "Metformin", 1, "500mg BID", "", 22.40, 22.40, false));
        prescriptions.add(new Prescription("PRE135", "CON135", "23", "DOC005", "Hypertension", pmSet135, "05-09-2025", "active", 22.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet136 = new SetQueueArray<>();
        pmSet136.add(new PrescribedMedicine("PM136", "PRE136", "MED009", "Sertraline", 1, "50mg daily", "", 45.60, 45.60, false));
        prescriptions.add(new Prescription("PRE136", "CON136", "24", "DOC006", "Hypertension", pmSet136, "05-09-2025", "active", 45.60, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet137 = new SetQueueArray<>();
        pmSet137.add(new PrescribedMedicine("PM137", "PRE137", "MED028", "Budesonide", 1, "1 puff BID", "", 52.30, 52.30, false));
        prescriptions.add(new Prescription("PRE137", "CON137", "11", "DOC007", "Hypertension", pmSet137, "05-09-2025", "active", 52.30, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet138 = new SetQueueArray<>();
        pmSet138.add(new PrescribedMedicine("PM138", "PRE138", "MED038", "Alprazolam", 1, "0.5mg nightly", "", 22.80, 22.80, false));
        prescriptions.add(new Prescription("PRE138", "CON138", "8", "DOC008", "Hypertension", pmSet138, "05-09-2025", "active", 22.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet139 = new SetQueueArray<>();
        pmSet139.add(new PrescribedMedicine("PM139", "PRE139", "MED039", "Gabapentin", 1, "300mg nightly", "", 31.40, 31.40, false));
        prescriptions.add(new Prescription("PRE139", "CON139", "15", "DOC009", "Hypertension", pmSet139, "05-09-2025", "active", 31.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet140 = new SetQueueArray<>();
        pmSet140.add(new PrescribedMedicine("PM140", "PRE140", "MED010", "Atorvastatin", 1, "20mg nightly", "", 58.90, 58.90, false));
        prescriptions.add(new Prescription("PRE140", "CON140", "20", "DOC010", "Hypertension", pmSet140, "05-09-2025", "active", 58.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet141 = new SetQueueArray<>();
        pmSet141.add(new PrescribedMedicine("PM141", "PRE141", "MED011", "Aspirin", 1, "75mg daily", "", 6.50, 6.50, false));
        prescriptions.add(new Prescription("PRE141", "CON141", "1", "DOC011", "Diabetes", pmSet141, "05-09-2025", "active", 6.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet142 = new SetQueueArray<>();
        pmSet142.add(new PrescribedMedicine("PM142", "PRE142", "MED012", "Insulin", 1, "Sliding scale", "", 120.00, 120.00, false));
        prescriptions.add(new Prescription("PRE142", "CON142", "1", "DOC012", "Diabetes", pmSet142, "05-09-2025", "active", 120.00, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet143 = new SetQueueArray<>();
        pmSet143.add(new PrescribedMedicine("PM143", "PRE143", "MED013", "Loratadine", 1, "10mg daily", "", 16.80, 16.80, false));
        prescriptions.add(new Prescription("PRE143", "CON143", "1", "DOC013", "Diabetes", pmSet143, "05-09-2025", "active", 16.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet144 = new SetQueueArray<>();
        pmSet144.add(new PrescribedMedicine("PM144", "PRE144", "MED014", "Lisinopril", 1, "10mg daily", "", 28.40, 28.40, false));
        prescriptions.add(new Prescription("PRE144", "CON144", "1", "DOC014", "Diabetes", pmSet144, "05-09-2025", "active", 28.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet145 = new SetQueueArray<>();
        pmSet145.add(new PrescribedMedicine("PM145", "PRE145", "MED015", "Fluoxetine", 1, "20mg daily", "", 42.30, 42.30, false));
        prescriptions.add(new Prescription("PRE145", "CON145", "25", "DOC015", "Diabetes", pmSet145, "05-09-2025", "active", 42.30, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet146 = new SetQueueArray<>();
        pmSet146.add(new PrescribedMedicine("PM146", "PRE146", "MED016", "Simvastatin", 1, "20mg nightly", "", 35.70, 35.70, false));
        prescriptions.add(new Prescription("PRE146", "CON146", "26", "DOC016", "Diabetes", pmSet146, "05-09-2025", "active", 35.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet147 = new SetQueueArray<>();
        pmSet147.add(new PrescribedMedicine("PM147", "PRE147", "MED017", "Montelukast", 1, "10mg nightly", "", 38.90, 38.90, false));
        prescriptions.add(new Prescription("PRE147", "CON147", "27", "DOC017", "Diabetes", pmSet147, "05-09-2025", "active", 38.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet148 = new SetQueueArray<>();
        pmSet148.add(new PrescribedMedicine("PM148", "PRE148", "MED018", "Pantoprazole", 1, "40mg daily", "", 31.20, 31.20, false));
        prescriptions.add(new Prescription("PRE148", "CON148", "28", "DOC018", "Diabetes", pmSet148, "05-09-2025", "active", 31.20, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet149 = new SetQueueArray<>();
        pmSet149.add(new PrescribedMedicine("PM149", "PRE149", "MED019", "Duloxetine", 1, "30mg daily", "", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE149", "CON149", "29", "DOC019", "Diabetes", pmSet149, "05-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet150 = new SetQueueArray<>();
        pmSet150.add(new PrescribedMedicine("PM150", "PRE150", "MED020", "Losartan", 1, "50mg daily", "", 29.60, 29.60, false));
        prescriptions.add(new Prescription("PRE150", "CON150", "30", "DOC020", "Diabetes", pmSet150, "05-09-2025", "active", 29.60, false));
        prescriptions.add(new Prescription("PRE151", "CON151", "11", "DOC001", "CHF", new SetQueueArray<>(), "05-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE152", "CON152", "32", "DOC002", "ADHD", new SetQueueArray<>(), "05-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE153", "CON153", "33", "DOC003", "Alzheimer's", new SetQueueArray<>(), "05-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE154", "CON154", "34", "DOC005", "Gestational Diabetes", new SetQueueArray<>(), "05-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE155", "CON155", "35", "DOC006", "OCD", new SetQueueArray<>(), "05-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE156", "CON156", "36", "DOC007", "Rosacea", new SetQueueArray<>(), "05-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE157", "CON157", "2", "DOC008", "Anaphylaxis", new SetQueueArray<>(), "05-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE158", "CON158", "7", "DOC009", "Bone Marrow", new SetQueueArray<>(), "05-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE159", "CON159", "11", "DOC010", "Nuclear Scan", new SetQueueArray<>(), "05-09-2025", "active", 0.0, false));
        prescriptions.add(new Prescription("PRE160", "CON160", "18", "DOC011", "Ovarian Cyst", new SetQueueArray<>(), "05-09-2025", "active", 0.0, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet161 = new SetQueueArray<>();
        pmSet161.add(new PrescribedMedicine("PM161", "PRE161", "MED012", "Insulin", 1, "Sliding scale", "", 120.00, 120.00, false));
        prescriptions.add(new Prescription("PRE161", "CON161", "1", "DOC012", "Testicular CA", pmSet161, "06-09-2025", "active", 120.00, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet162 = new SetQueueArray<>();
        pmSet162.add(new PrescribedMedicine("PM162", "PRE162", "MED013", "Loratadine", 1, "10mg daily", "", 16.80, 16.80, false));
        prescriptions.add(new Prescription("PRE162", "CON162", "2", "DOC013", "Autoimmune", pmSet162, "06-09-2025", "active", 16.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet163 = new SetQueueArray<>();
        pmSet163.add(new PrescribedMedicine("PM163", "PRE163", "MED018", "Pantoprazole", 1, "40mg daily", "", 31.20, 31.20, false));
        prescriptions.add(new Prescription("PRE163", "CON163", "3", "DOC014", "Celiac", pmSet163, "06-09-2025", "active", 31.20, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet164 = new SetQueueArray<>();
        pmSet164.add(new PrescribedMedicine("PM164", "PRE164", "MED023", "Diclofenac", 1, "50mg BID", "", 14.70, 14.70, false));
        prescriptions.add(new Prescription("PRE164", "CON164", "4", "DOC015", "Gout", pmSet164, "06-09-2025", "active", 14.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet165 = new SetQueueArray<>();
        pmSet165.add(new PrescribedMedicine("PM165", "PRE165", "MED016", "Simvastatin", 1, "20mg nightly", "", 35.70, 35.70, false));
        prescriptions.add(new Prescription("PRE165", "CON165", "5", "DOC016", "ILD", pmSet165, "06-09-2025", "active", 35.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet166 = new SetQueueArray<>();
        pmSet166.add(new PrescribedMedicine("PM166", "PRE166", "MED017", "Montelukast", 1, "10mg nightly", "", 38.90, 38.90, false));
        prescriptions.add(new Prescription("PRE166", "CON166", "6", "DOC017", "Diabetic Nephropathy", pmSet166, "06-09-2025", "active", 38.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet167 = new SetQueueArray<>();
        pmSet167.add(new PrescribedMedicine("PM167", "PRE167", "MED019", "Duloxetine", 1, "30mg daily", "", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE167", "CON167", "7", "DOC018", "Sickle Cell", pmSet167, "06-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet168 = new SetQueueArray<>();
        pmSet168.add(new PrescribedMedicine("PM168", "PRE168", "MED040", "Clonazepam", 1, "0.5mg nightly", "", 19.60, 19.60, false));
        prescriptions.add(new Prescription("PRE168", "CON168", "8", "DOC019", "HIV", pmSet168, "06-09-2025", "active", 19.60, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet169 = new SetQueueArray<>();
        pmSet169.add(new PrescribedMedicine("PM169", "PRE169", "MED001", "Paracetamol", 1, "500mg QID", "", 8.50, 8.50, false));
        prescriptions.add(new Prescription("PRE169", "CON169", "9", "DOC020", "Epidural", pmSet169, "06-09-2025", "active", 8.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet170 = new SetQueueArray<>();
        pmSet170.add(new PrescribedMedicine("PM170", "PRE170", "MED010", "Atorvastatin", 1, "20mg nightly", "", 58.90, 58.90, false));
        prescriptions.add(new Prescription("PRE170", "CON170", "10", "DOC001", "PAD", pmSet170, "06-09-2025", "active", 58.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet171 = new SetQueueArray<>();
        pmSet171.add(new PrescribedMedicine("PM171", "PRE171", "MED022", "Cephalexin", 1, "500mg QID", "", 18.90, 18.90, false));
        prescriptions.add(new Prescription("PRE171", "CON171", "11", "DOC002", "Cystic Fibrosis", pmSet171, "06-09-2025", "active", 18.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet172 = new SetQueueArray<>();
        pmSet172.add(new PrescribedMedicine("PM172", "PRE172", "MED032", "Metoprolol", 1, "50mg BID", "", 24.60, 24.60, false));
        prescriptions.add(new Prescription("PRE172", "CON172", "2", "DOC003", "Huntington's", pmSet172, "06-09-2025", "active", 24.60, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet173 = new SetQueueArray<>();
        pmSet173.add(new PrescribedMedicine("PM173", "PRE173", "MED006", "Metformin", 1, "500mg BID", "", 22.40, 22.40, false));
        prescriptions.add(new Prescription("PRE173", "CON173", "13", "DOC005", "PCOS", pmSet173, "06-09-2025", "active", 22.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet174 = new SetQueueArray<>();
        pmSet174.add(new PrescribedMedicine("PM174", "PRE174", "MED019", "Duloxetine", 1, "30mg daily", "", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE174", "CON174", "14", "DOC006", "Eating Disorder", pmSet174, "06-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet175 = new SetQueueArray<>();
        pmSet175.add(new PrescribedMedicine("PM175", "PRE175", "MED028", "Budesonide", 1, "1 puff BID", "", 52.30, 52.30, false));
        prescriptions.add(new Prescription("PRE175", "CON175", "15", "DOC007", "Vitiligo", pmSet175, "06-09-2025", "active", 52.30, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet176 = new SetQueueArray<>();
        pmSet176.add(new PrescribedMedicine("PM176", "PRE176", "MED001", "Paracetamol", 1, "500mg QID", "", 8.50, 8.50, false));
        prescriptions.add(new Prescription("PRE176", "CON176", "16", "DOC008", "Burn Injury", pmSet176, "06-09-2025", "active", 8.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet177 = new SetQueueArray<>();
        pmSet177.add(new PrescribedMedicine("PM177", "PRE177", "MED039", "Gabapentin", 1, "300mg nightly", "", 31.40, 31.40, false));
        prescriptions.add(new Prescription("PRE177", "CON177", "17", "DOC009", "Palliative", pmSet177, "06-09-2025", "active", 31.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet178 = new SetQueueArray<>();
        pmSet178.add(new PrescribedMedicine("PM178", "PRE178", "MED010", "Atorvastatin", 1, "20mg nightly", "", 58.90, 58.90, false));
        prescriptions.add(new Prescription("PRE178", "CON178", "18", "DOC010", "Mammogram", pmSet178, "06-09-2025", "active", 58.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet179 = new SetQueueArray<>();
        pmSet179.add(new PrescribedMedicine("PM179", "PRE179", "MED011", "Aspirin", 1, "75mg daily", "", 6.50, 6.50, false));
        prescriptions.add(new Prescription("PRE179", "CON179", "19", "DOC011", "Uterine Fibroid", pmSet179, "06-09-2025", "active", 6.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet180 = new SetQueueArray<>();
        pmSet180.add(new PrescribedMedicine("PM180", "PRE180", "MED012", "Insulin", 1, "Sliding scale", "", 120.00, 120.00, false));
        prescriptions.add(new Prescription("PRE180", "CON180", "20", "DOC012", "Vasectomy", pmSet180, "06-09-2025", "active", 120.00, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet181 = new SetQueueArray<>();
        pmSet181.add(new PrescribedMedicine("PM181", "PRE181", "MED013", "Loratadine", 1, "10mg daily", "", 16.80, 16.80, false));
        prescriptions.add(new Prescription("PRE181", "CON181", "21", "DOC013", "Iron Deficiency", pmSet181, "06-09-2025", "active", 16.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet182 = new SetQueueArray<>();
        pmSet182.add(new PrescribedMedicine("PM182", "PRE182", "MED014", "Lisinopril", 1, "10mg daily", "", 28.40, 28.40, false));
        prescriptions.add(new Prescription("PRE182", "CON182", "22", "DOC014", "Hepatitis C", pmSet182, "06-09-2025", "active", 28.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet183 = new SetQueueArray<>();
        pmSet183.add(new PrescribedMedicine("PM183", "PRE183", "MED015", "Fluoxetine", 1, "20mg daily", "", 42.30, 42.30, false));
        prescriptions.add(new Prescription("PRE183", "CON183", "23", "DOC015", "Carpal Tunnel", pmSet183, "06-09-2025", "active", 42.30, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet184 = new SetQueueArray<>();
        pmSet184.add(new PrescribedMedicine("PM184", "PRE184", "MED016", "Simvastatin", 1, "20mg nightly", "", 35.70, 35.70, false));
        prescriptions.add(new Prescription("PRE184", "CON184", "24", "DOC016", "Chronic Bronchitis", pmSet184, "06-09-2025", "active", 35.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet185 = new SetQueueArray<>();
        pmSet185.add(new PrescribedMedicine("PM185", "PRE185", "MED017", "Montelukast", 1, "10mg nightly", "", 38.90, 38.90, false));
        prescriptions.add(new Prescription("PRE185", "CON185", "25", "DOC017", "AKI", pmSet185, "06-09-2025", "active", 38.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet186 = new SetQueueArray<>();
        pmSet186.add(new PrescribedMedicine("PM186", "PRE186", "MED018", "Pantoprazole", 1, "40mg daily", "", 31.20, 31.20, false));
        prescriptions.add(new Prescription("PRE186", "CON186", "26", "DOC018", "Platelet Disorder", pmSet186, "06-09-2025", "active", 31.20, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet187 = new SetQueueArray<>();
        pmSet187.add(new PrescribedMedicine("PM187", "PRE187", "MED019", "Duloxetine", 1, "30mg daily", "", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE187", "CON187", "27", "DOC019", "Malaria", pmSet187, "06-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet188 = new SetQueueArray<>();
        pmSet188.add(new PrescribedMedicine("PM188", "PRE188", "MED040", "Clonazepam", 1, "0.5mg nightly", "", 19.60, 19.60, false));
        prescriptions.add(new Prescription("PRE188", "CON188", "28", "DOC020", "Spinal Anesthesia", pmSet188, "06-09-2025", "active", 19.60, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet189 = new SetQueueArray<>();
        pmSet189.add(new PrescribedMedicine("PM189", "PRE189", "MED001", "Paracetamol", 1, "500mg QID", "", 8.50, 8.50, false));
        prescriptions.add(new Prescription("PRE189", "CON189", "29", "DOC001", "MVP", pmSet189, "06-09-2025", "active", 8.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet190 = new SetQueueArray<>();
        pmSet190.add(new PrescribedMedicine("PM190", "PRE190", "MED010", "Atorvastatin", 1, "20mg nightly", "", 58.90, 58.90, false));
        prescriptions.add(new Prescription("PRE190", "CON190", "30", "DOC002", "Cerebral Palsy", pmSet190, "06-09-2025", "active", 58.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet191 = new SetQueueArray<>();
        pmSet191.add(new PrescribedMedicine("PM191", "PRE191", "MED011", "Aspirin", 1, "75mg daily", "", 6.50, 6.50, false));
        prescriptions.add(new Prescription("PRE191", "CON191", "31", "DOC003", "Migraine Prevention", pmSet191, "06-09-2025", "active", 6.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet192 = new SetQueueArray<>();
        pmSet192.add(new PrescribedMedicine("PM192", "PRE192", "MED012", "Insulin", 1, "Sliding scale", "", 120.00, 120.00, false));
        prescriptions.add(new Prescription("PRE192", "CON192", "32", "DOC005", "Cushing's", pmSet192, "06-09-2025", "active", 120.00, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet193 = new SetQueueArray<>();
        pmSet193.add(new PrescribedMedicine("PM193", "PRE193", "MED013", "Loratadine", 1, "10mg daily", "", 16.80, 16.80, false));
        prescriptions.add(new Prescription("PRE193", "CON193", "33", "DOC006", "Panic Disorder", pmSet193, "06-09-2025", "active", 16.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet194 = new SetQueueArray<>();
        pmSet194.add(new PrescribedMedicine("PM194", "PRE194", "MED014", "Lisinopril", 1, "10mg daily", "", 28.40, 28.40, false));
        prescriptions.add(new Prescription("PRE194", "CON194", "34", "DOC007", "Basal Cell CA", pmSet194, "06-09-2025", "active", 28.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet195 = new SetQueueArray<>();
        pmSet195.add(new PrescribedMedicine("PM195", "PRE195", "MED015", "Fluoxetine", 1, "20mg daily", "", 42.30, 42.30, false));
        prescriptions.add(new Prescription("PRE195", "CON195", "35", "DOC008", "Hypothermia", pmSet195, "06-09-2025", "active", 42.30, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet196 = new SetQueueArray<>();
        pmSet196.add(new PrescribedMedicine("PM196", "PRE196", "MED016", "Simvastatin", 1, "20mg nightly", "", 35.70, 35.70, false));
        prescriptions.add(new Prescription("PRE196", "CON196", "36", "DOC009", "Lymphoma", pmSet196, "06-09-2025", "active", 35.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet197 = new SetQueueArray<>();
        pmSet197.add(new PrescribedMedicine("PM197", "PRE197", "MED017", "Montelukast", 1, "10mg nightly", "", 38.90, 38.90, false));
        prescriptions.add(new Prescription("PRE197", "CON197", "37", "DOC010", "Bone Density", pmSet197, "06-09-2025", "active", 38.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet198 = new SetQueueArray<>();
        pmSet198.add(new PrescribedMedicine("PM198", "PRE198", "MED018", "Pantoprazole", 1, "40mg daily", "", 31.20, 31.20, false));
        prescriptions.add(new Prescription("PRE198", "CON198", "38", "DOC011", "PID", pmSet198, "06-09-2025", "active", 31.20, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet199 = new SetQueueArray<>();
        pmSet199.add(new PrescribedMedicine("PM199", "PRE199", "MED019", "Duloxetine", 1, "30mg daily", "", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE199", "CON199", "39", "DOC012", "Penile CA", pmSet199, "06-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet200 = new SetQueueArray<>();
        pmSet200.add(new PrescribedMedicine("PM200", "PRE200", "MED040", "Clonazepam", 1, "0.5mg nightly", "", 19.60, 19.60, false));
        prescriptions.add(new Prescription("PRE200", "CON200", "40", "DOC013", "CFS", pmSet200, "06-09-2025", "active", 19.60, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet201 = new SetQueueArray<>();
        pmSet201.add(new PrescribedMedicine("PM201", "PRE201", "MED018", "Pantoprazole", 1, "40mg daily", "", 31.20, 31.20, false));
        prescriptions.add(new Prescription("PRE201", "CON201", "1", "DOC014", "Pancreatic Disorder", pmSet201, "07-09-2025", "active", 31.20, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet202 = new SetQueueArray<>();
        pmSet202.add(new PrescribedMedicine("PM202", "PRE202", "MED023", "Diclofenac", 1, "50mg BID", "", 14.70, 14.70, false));
        prescriptions.add(new Prescription("PRE202", "CON202", "2", "DOC015", "Tendonitis", pmSet202, "07-09-2025", "active", 14.70, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet203 = new SetQueueArray<>();
        pmSet203.add(new PrescribedMedicine("PM203", "PRE203", "MED001", "Paracetamol", 1, "500mg QID", "", 8.50, 8.50, false));
        prescriptions.add(new Prescription("PRE203", "CON203", "3", "DOC016", "Pneumothorax", pmSet203, "07-09-2025", "active", 8.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet204 = new SetQueueArray<>();
        pmSet204.add(new PrescribedMedicine("PM204", "PRE204", "MED022", "Cephalexin", 1, "500mg QID", "", 18.90, 18.90, false));
        prescriptions.add(new Prescription("PRE204", "CON204", "4", "DOC017", "Glomerulonephritis", pmSet204, "07-09-2025", "active", 18.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet205 = new SetQueueArray<>();
        pmSet205.add(new PrescribedMedicine("PM205", "PRE205", "MED019", "Duloxetine", 1, "30mg daily", "", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE205", "CON205", "5", "DOC018", "Aplastic Anemia", pmSet205, "07-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet206 = new SetQueueArray<>();
        pmSet206.add(new PrescribedMedicine("PM206", "PRE206", "MED002", "Amoxicillin", 1, "500mg TID", "Complete course", 15.80, 15.80, false));
        prescriptions.add(new Prescription("PRE206", "CON206", "6", "DOC019", "Dengue", pmSet206, "07-09-2025", "active", 15.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet207 = new SetQueueArray<>();
        pmSet207.add(new PrescribedMedicine("PM207", "PRE207", "MED040", "Clonazepam", 1, "0.5mg nightly", "", 19.60, 19.60, false));
        prescriptions.add(new Prescription("PRE207", "CON207", "7", "DOC020", "Nerve Block", pmSet207, "07-09-2025", "active", 19.60, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet208 = new SetQueueArray<>();
        pmSet208.add(new PrescribedMedicine("PM208", "PRE208", "MED010", "Atorvastatin", 1, "20mg nightly", "", 58.90, 58.90, false));
        prescriptions.add(new Prescription("PRE208", "CON208", "8", "DOC001", "Aortic Stenosis", pmSet208, "07-09-2025", "active", 58.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet209 = new SetQueueArray<>();
        pmSet209.add(new PrescribedMedicine("PM209", "PRE209", "MED031", "Naproxen", 1, "250mg BID", "", 11.40, 11.40, false));
        prescriptions.add(new Prescription("PRE209", "CON209", "9", "DOC002", "Down Syndrome", pmSet209, "07-09-2025", "active", 11.40, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet210 = new SetQueueArray<>();
        pmSet210.add(new PrescribedMedicine("PM210", "PRE210", "MED003", "Ibuprofen", 1, "400mg PRN", "", 12.90, 12.90, false));
        prescriptions.add(new Prescription("PRE210", "CON210", "10", "DOC003", "Trigeminal Neuralgia", pmSet210, "07-09-2025", "active", 12.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet211 = new SetQueueArray<>();
        pmSet211.add(new PrescribedMedicine("PM211", "PRE211", "MED032", "Metoprolol", 1, "50mg BID", "", 24.60, 24.60, false));
        prescriptions.add(new Prescription("PRE211", "CON211", "11", "DOC005", "Addison's", pmSet211, "07-09-2025", "active", 24.60, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet212 = new SetQueueArray<>();
        pmSet212.add(new PrescribedMedicine("PM212", "PRE212", "MED009", "Sertraline", 1, "50mg daily", "", 45.60, 45.60, false));
        prescriptions.add(new Prescription("PRE212", "CON212", "12", "DOC006", "Schizophrenia", pmSet212, "07-09-2025", "active", 45.60, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet213 = new SetQueueArray<>();
        pmSet213.add(new PrescribedMedicine("PM213", "PRE213", "MED028", "Budesonide", 1, "1 puff BID", "", 52.30, 52.30, false));
        prescriptions.add(new Prescription("PRE213", "CON213", "13", "DOC007", "Hidradenitis", pmSet213, "07-09-2025", "active", 52.30, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet214 = new SetQueueArray<>();
        pmSet214.add(new PrescribedMedicine("PM214", "PRE214", "MED008", "Salbutamol", 1, "2 puffs PRN", "", 28.90, 28.90, false));
        prescriptions.add(new Prescription("PRE214", "CON214", "14", "DOC008", "Sepsis", pmSet214, "07-09-2025", "active", 28.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet215 = new SetQueueArray<>();
        pmSet215.add(new PrescribedMedicine("PM215", "PRE215", "MED019", "Duloxetine", 1, "30mg daily", "", 67.80, 67.80, false));
        prescriptions.add(new Prescription("PRE215", "CON215", "15", "DOC009", "Mesothelioma", pmSet215, "07-09-2025", "active", 67.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet216 = new SetQueueArray<>();
        pmSet216.add(new PrescribedMedicine("PM216", "PRE216", "MED010", "Atorvastatin", 1, "20mg nightly", "", 58.90, 58.90, false));
        prescriptions.add(new Prescription("PRE216", "CON216", "16", "DOC010", "PET Scan", pmSet216, "07-09-2025", "active", 58.90, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet217 = new SetQueueArray<>();
        pmSet217.add(new PrescribedMedicine("PM217", "PRE217", "MED011", "Aspirin", 1, "75mg daily", "", 6.50, 6.50, false));
        prescriptions.add(new Prescription("PRE217", "CON217", "17", "DOC011", "Ectopic Pregnancy", pmSet217, "07-09-2025", "active", 6.50, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet218 = new SetQueueArray<>();
        pmSet218.add(new PrescribedMedicine("PM218", "PRE218", "MED012", "Insulin", 1, "Sliding scale", "", 120.00, 120.00, false));
        prescriptions.add(new Prescription("PRE218", "CON218", "18", "DOC012", "Hydrocele", pmSet218, "07-09-2025", "active", 120.00, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet219 = new SetQueueArray<>();
        pmSet219.add(new PrescribedMedicine("PM219", "PRE219", "MED013", "Loratadine", 1, "10mg daily", "", 16.80, 16.80, false));
        prescriptions.add(new Prescription("PRE219", "CON219", "19", "DOC013", "Osteomalacia", pmSet219, "07-09-2025", "active", 16.80, false));
        SetAndQueueInterface<PrescribedMedicine> pmSet220 = new SetQueueArray<>();
        pmSet220.add(new PrescribedMedicine("PM220", "PRE220", "MED014", "Lisinopril", 1, "10mg daily", "", 28.40, 28.40, false));
        prescriptions.add(new Prescription("PRE220", "CON220", "20", "DOC014", "Esophageal Cancer", pmSet220, "07-09-2025", "active", 28.40, false));

        // Add some test prescriptions with mixed dispensed/non-dispensed medicines for testing update functionality
        SetAndQueueInterface<PrescribedMedicine> pmSetTest1 = new SetQueueArray<>();
        pmSetTest1.add(new PrescribedMedicine("PMT001", "PRETEST1", "MED001", "Paracetamol", 2, "500mg QID", "With water", 8.50, 17.00, true)); // DISPENSED
        pmSetTest1.add(new PrescribedMedicine("PMT002", "PRETEST1", "MED003", "Ibuprofen", 1, "200mg PRN", "After food", 12.90, 12.90, false)); // NOT DISPENSED
        pmSetTest1.add(new PrescribedMedicine("PMT003", "PRETEST1", "MED006", "Metformin", 1, "500mg BID", "With meals", 22.40, 22.40, false)); // NOT DISPENSED
        prescriptions.add(new Prescription("PRETEST1", "CONTEST1", "1", "DOC001", "Mixed Test Prescription", pmSetTest1, "08-09-2025", "active", 52.30, true));

        SetAndQueueInterface<PrescribedMedicine> pmSetTest2 = new SetQueueArray<>();
        pmSetTest2.add(new PrescribedMedicine("PMT004", "PRETEST2", "MED007", "Amlodipine", 1, "5mg daily", "Morning", 32.80, 32.80, true)); // DISPENSED
        pmSetTest2.add(new PrescribedMedicine("PMT005", "PRETEST2", "MED008", "Salbutamol", 1, "2 puffs PRN", "Inhaler", 28.90, 28.90, true)); // DISPENSED
        prescriptions.add(new Prescription("PRETEST2", "CONTEST2", "2", "DOC002", "All Dispensed Test", pmSetTest2, "08-09-2025", "active", 61.70, true));

        SetAndQueueInterface<PrescribedMedicine> pmSetTest3 = new SetQueueArray<>();
        pmSetTest3.add(new PrescribedMedicine("PMT006", "PRETEST3", "MED009", "Sertraline", 1, "50mg daily", "Morning", 45.60, 45.60, false)); // NOT DISPENSED
        pmSetTest3.add(new PrescribedMedicine("PMT007", "PRETEST3", "MED010", "Atorvastatin", 1, "20mg nightly", "Evening", 58.90, 58.90, false)); // NOT DISPENSED
        prescriptions.add(new Prescription("PRETEST3", "CONTEST3", "3", "DOC003", "All Non-Dispensed Test", pmSetTest3, "08-09-2025", "active", 104.50, true));

        return prescriptions;
    }
} 